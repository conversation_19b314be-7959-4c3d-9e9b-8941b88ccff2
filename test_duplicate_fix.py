#!/usr/bin/env python3
"""
Test script để kiểm tra sửa lỗi duplicate brands
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from logic import BrandSorter

def test_used_brands_tracking():
    """Test used_brands tracking logic"""
    print("🧪 Testing Used Brands Tracking...")
    
    sorter = BrandSorter()
    
    # Thêm sample brands
    brands = [
        {"name": "Brand1", "category": "ELHA", "type": "Skincare", "sku_review": 5, "gmv": 1000000, "assigned": False},
        {"name": "Brand2", "category": "ELHA", "type": "Makeup", "sku_review": 10, "gmv": 2000000, "assigned": False},
        {"name": "Brand3", "category": "HB", "type": "Shampoo", "sku_review": 3, "gmv": 500000, "assigned": False},
        {"name": "Brand4", "category": "HB", "type": "Soap", "sku_review": 6, "gmv": 800000, "assigned": False},
    ]
    
    sorter.brands = brands
    sorter.categories = set(brand["category"] for brand in brands)
    
    # Test initial state
    assert len(sorter.used_brands) == 0, "Initial used_brands should be empty"
    
    # Simulate adding brands to used_brands
    sorter.used_brands.add("Brand1")
    sorter.used_brands.add("Brand2")
    
    print(f"✅ Used brands: {sorter.used_brands}")
    assert len(sorter.used_brands) == 2, "Should have 2 used brands"
    
    return True

def test_filter_suitable_brands():
    """Test filter_suitable_brands excludes used brands"""
    print("\n🧪 Testing Filter Suitable Brands...")
    
    sorter = BrandSorter()
    
    # Setup brands
    brands = [
        {"name": "Brand1", "category": "ELHA", "sku_review": 5, "gmv": 1000000, "assigned": False},
        {"name": "Brand2", "category": "ELHA", "sku_review": 10, "gmv": 2000000, "assigned": False},
        {"name": "Brand3", "category": "HB", "sku_review": 3, "gmv": 500000, "assigned": False},
    ]
    
    sorter.brands = brands
    sorter.categories = set(brand["category"] for brand in brands)
    
    # Mark Brand1 as used
    sorter.used_brands.add("Brand1")
    
    # Create categorized brands
    categorized_brands = {
        "ELHA": [brands[0], brands[1]],  # Brand1, Brand2
        "HB": [brands[2]]  # Brand3
    }
    
    # Create timeslot
    timeslot = {
        "name": "19:00-20:00",
        "start_time": "19:00",
        "end_time": "20:00",
        "allowed_categories": ["ELHA", "HB"],
        "high_traffic": True
    }
    
    # Test filter
    remaining_brands = brands  # All brands available
    suitable_brands = sorter.filter_suitable_brands(remaining_brands, timeslot, categorized_brands)
    
    # Should exclude Brand1 (used) but include Brand2, Brand3
    suitable_names = [b["name"] for b in suitable_brands]
    print(f"✅ Suitable brands: {suitable_names}")
    
    assert "Brand1" not in suitable_names, "Should exclude used Brand1"
    assert "Brand2" in suitable_names, "Should include unused Brand2"
    assert "Brand3" in suitable_names, "Should include unused Brand3"
    
    return True

def test_fallback_selection():
    """Test fallback selection excludes used brands"""
    print("\n🧪 Testing Fallback Selection...")
    
    sorter = BrandSorter()
    
    # Setup brands
    brands = [
        {"name": "Brand1", "category": "ELHA", "sku_review": 5, "gmv": 1000000},
        {"name": "Brand2", "category": "ELHA", "sku_review": 10, "gmv": 2000000},
        {"name": "Brand3", "category": "HB", "sku_review": 9, "gmv": 500000},
    ]
    
    # Mark Brand1 as used
    sorter.used_brands.add("Brand1")
    
    # Create timeslot with category quotas
    timeslot = {
        "name": "19:00-20:00",
        "category_quotas": {"ELHA": 15, "HB": 9}
    }
    
    # Test fallback selection
    result = sorter.fallback_selection(brands, timeslot)
    
    # Should only include unused brands
    selected_brands = result["19:00-20:00"]
    selected_names = [b["name"] for b in selected_brands]
    
    print(f"✅ Fallback selected: {selected_names}")
    
    assert "Brand1" not in selected_names, "Fallback should exclude used Brand1"
    assert len(selected_names) <= 2, "Should only select unused brands"
    
    return True

def test_duplicate_validation():
    """Test duplicate validation in results"""
    print("\n🧪 Testing Duplicate Validation...")
    
    sorter = BrandSorter()
    
    # Setup timeslots
    sorter.timeslots = [
        {
            "name": "19:00-20:00",
            "allowed_categories": ["ELHA", "HB"],
            "high_traffic": True,
            "category_quotas": {"ELHA": 15, "HB": 9}
        },
        {
            "name": "21:00-22:00",
            "allowed_categories": ["ELHA", "HB"],
            "high_traffic": False,
            "category_quotas": {"ELHA": 10, "HB": 5}
        }
    ]
    
    # Create results with duplicates
    sorter.results = {
        "19:00-20:00": [
            {"name": "Brand1", "category": "ELHA", "sku_review": 15, "gmv": 1000000},
            {"name": "Brand2", "category": "HB", "sku_review": 9, "gmv": 500000}
        ],
        "21:00-22:00": [
            {"name": "Brand1", "category": "ELHA", "sku_review": 10, "gmv": 1000000},  # DUPLICATE!
            {"name": "Brand3", "category": "HB", "sku_review": 5, "gmv": 300000}
        ]
    }
    
    # Test validation
    errors = sorter.validate_timeslot_assignment()
    duplicate_errors = [e for e in errors if "DUPLICATE" in e]
    
    print(f"✅ Duplicate validation: {len(duplicate_errors)} errors found")
    for error in duplicate_errors:
        print(f"   - {error}")
    
    assert len(duplicate_errors) > 0, "Should detect duplicate Brand1"
    
    return True

def main():
    """Run all duplicate fix tests"""
    print("🚀 Starting Duplicate Fix Tests...\n")
    
    try:
        test_used_brands_tracking()
        test_filter_suitable_brands()
        test_fallback_selection()
        test_duplicate_validation()
        
        print("\n🎉 All duplicate fix tests passed!")
        print("\n📋 Summary of duplicate fixes:")
        print("   ✅ Used brands tracking working correctly")
        print("   ✅ Filter suitable brands excludes used brands")
        print("   ✅ Fallback selection excludes used brands")
        print("   ✅ Duplicate validation detects violations")
        print("   ✅ Multiple layers of duplicate prevention implemented")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Test script để kiểm tra các sửa lỗi:
1. Category quotas trong timeslots
2. SUM GMV thay vì AVG GMV
3. Validation logic mới
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from logic import BrandSorter

def test_category_quotas():
    """Test category quotas functionality"""
    print("🧪 Testing Category Quotas...")

    sorter = BrandSorter()

    # Thêm sample brands trực tiếp vào sorter.brands
    brands = [
        {"name": "Brand1", "category": "ELHA", "type": "Skincare", "sku_review": 5, "gmv": 1000000, "assigned": False},
        {"name": "Brand2", "category": "ELHA", "type": "Makeup", "sku_review": 10, "gmv": 2000000, "assigned": False},
        {"name": "Brand3", "category": "HB", "type": "Shampoo", "sku_review": 3, "gmv": 500000, "assigned": False},
        {"name": "Brand4", "category": "HB", "type": "Soap", "sku_review": 6, "gmv": 800000, "assigned": False},
        {"name": "Brand5", "category": "ELHA", "type": "Serum", "sku_review": 8, "gmv": 1500000, "assigned": False},
    ]

    # Thêm brands và categories trực tiếp
    sorter.brands = brands
    sorter.categories = set(brand["category"] for brand in brands)
    
    # Thêm timeslot với category quotas
    category_quotas = {"ELHA": 15, "HB": 9}
    timeslot = sorter.add_timeslot(
        name="19:00-20:00",
        start_time="19:00",
        end_time="20:00",
        allowed_categories=["ELHA", "HB"],
        high_traffic=True,
        min_sku=24,  # 15 + 9
        category_quotas=category_quotas
    )
    
    print(f"✅ Created timeslot with category quotas: {timeslot['category_quotas']}")
    
    # Test SUM GMV calculation
    test_brands = brands[:3]
    sum_gmv = sorter.calculate_sum_gmv(test_brands)
    avg_gmv = sorter.calculate_avg_gmv(test_brands)
    
    expected_sum = sum(b["gmv"] for b in test_brands)
    expected_avg = expected_sum / len(test_brands)
    
    print(f"✅ SUM GMV: {sum_gmv:,.0f} (expected: {expected_sum:,.0f})")
    print(f"✅ AVG GMV: {avg_gmv:,.0f} (expected: {expected_avg:,.0f})")
    
    assert sum_gmv == expected_sum, f"SUM GMV calculation failed: {sum_gmv} != {expected_sum}"
    assert avg_gmv == expected_avg, f"AVG GMV calculation failed: {avg_gmv} != {expected_avg}"
    
    return True

def test_validation_logic():
    """Test validation logic với category quotas"""
    print("\n🧪 Testing Validation Logic...")

    sorter = BrandSorter()

    # Thêm brands trực tiếp
    brands = [
        {"name": "Brand1", "category": "ELHA", "sku_review": 10, "gmv": 1000000, "assigned": False},
        {"name": "Brand2", "category": "ELHA", "sku_review": 5, "gmv": 2000000, "assigned": False},
        {"name": "Brand3", "category": "HB", "sku_review": 9, "gmv": 500000, "assigned": False},
    ]

    sorter.brands = brands
    sorter.categories = set(brand["category"] for brand in brands)
    
    # Thêm timeslot với category quotas
    category_quotas = {"ELHA": 15, "HB": 9}
    sorter.add_timeslot(
        name="19:00-20:00",
        start_time="19:00",
        end_time="20:00",
        allowed_categories=["ELHA", "HB"],
        high_traffic=True,
        min_sku=24,
        category_quotas=category_quotas
    )
    
    # Tạo kết quả test với quota sai (ELHA cần 15 nhưng chỉ có 15, HB cần 9 nhưng chỉ có 9)
    # Brands hiện tại: ELHA=15 (10+5), HB=9 - đúng quota
    # Thay đổi để có lỗi
    test_brands = [
        {"name": "Brand1", "category": "ELHA", "sku_review": 10, "gmv": 1000000},
        {"name": "Brand2", "category": "ELHA", "sku_review": 3, "gmv": 2000000},  # Giảm từ 5 xuống 3
        {"name": "Brand3", "category": "HB", "sku_review": 9, "gmv": 500000},
    ]

    sorter.results = {
        "19:00-20:00": test_brands
    }

    # Test validation
    errors = sorter.validate_timeslot_assignment()
    print(f"✅ Validation errors found: {len(errors)}")
    for error in errors:
        print(f"   - {error}")

    # Should have errors vì ELHA thiếu (13 thay vì 15)
    assert len(errors) > 0, "Should have validation errors for incorrect quotas"
    
    return True

def test_prompt_generation():
    """Test prompt generation với category quotas nghiêm ngặt"""
    print("\n🧪 Testing New Strict Prompt Generation...")

    sorter = BrandSorter()

    # Thêm sample data
    brands = [
        {"name": "Brand1", "category": "ELHA", "type": "Skincare", "sku_review": 5, "gmv": 1000000, "timeslot_priority": 1.0},
    ]

    timeslot = {
        "name": "19:00-20:00",
        "start_time": "19:00",
        "end_time": "20:00",
        "allowed_categories": ["ELHA", "HB"],
        "high_traffic": True,
        "min_sku": 24,
        "category_quotas": {"ELHA": 15, "HB": 9}
    }

    context = {
        "season": "summer",
        "month": 6,
        "total_brands": 100,
        "avg_gmv": 1000000,
        "is_holiday_season": False
    }

    prompt = sorter.create_smart_prompt_for_timeslot(brands, timeslot, {}, context, 0.3)

    print("✅ Generated NEW STRICT prompt:")
    print("   - Contains 'QUOTA REQUIREMENTS':", "QUOTA REQUIREMENTS" in prompt)
    print("   - Contains 'CHÍNH XÁC 15 SKU reviews':", "CHÍNH XÁC 15 SKU reviews" in prompt)
    print("   - Contains 'CHÍNH XÁC 9 SKU reviews':", "CHÍNH XÁC 9 SKU reviews" in prompt)
    print("   - Contains 'ZERO TOLERANCE':", "ZERO TOLERANCE" in prompt)
    print("   - Contains 'SUM GMV BALANCE':", "SUM GMV BALANCE" in prompt)

    # Test system prompt
    system_prompt = sorter.get_smart_system_prompt()
    print("   - System prompt is strict:", "STRICT" in system_prompt)
    print("   - System prompt mentions zero tolerance:", "zero tolerance" in system_prompt)

    assert "QUOTA REQUIREMENTS" in prompt, "Prompt should contain strict quota requirements"
    assert "CHÍNH XÁC 15 SKU reviews" in prompt, "Prompt should contain exact ELHA quota"
    assert "CHÍNH XÁC 9 SKU reviews" in prompt, "Prompt should contain exact HB quota"
    assert "ZERO TOLERANCE" in prompt, "Prompt should emphasize zero tolerance"

    return True

def main():
    """Run all tests"""
    print("🚀 Starting Brand Sorter Fix Tests...\n")
    
    try:
        test_category_quotas()
        test_validation_logic()
        test_prompt_generation()
        
        print("\n🎉 All tests passed! Fixes are working correctly.")
        print("\n📋 Summary of fixes:")
        print("   ✅ Category quotas support added to timeslots")
        print("   ✅ SUM GMV calculation working correctly")
        print("   ✅ Validation logic updated for category quotas")
        print("   ✅ Prompt generation includes category quota information")
        print("   ✅ UI updated to pass category quotas to logic")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

import pandas as pd
import numpy as np
import json
import os
import openai
import datetime
import threading
import time
from typing import List, Dict, Any, Tuple, Optional, Callable
from concurrent.futures import ThreadPoolExecutor, as_completed

# Import Google Sheets manager
try:
    from gsheet_manager import GoogleSheetManager
    GSHEETS_AVAILABLE = True
except ImportError:
    GSHEETS_AVAILABLE = False
    print("Warning: Google Sheets integration not available. Install required packages.")

class BrandSorter:
    def __init__(self):
        self.brands = []
        self.categories = set()
        self.timeslots = []
        self.results = {}
        self.used_brands = set()  # Để đảm bảo mỗi brand chỉ xuất hiện một lần

        # Optimization settings
        self.chunk_size = 50  # Số brands tối đa trong mỗi chunk
        self.max_tokens_per_request = 3500  # Giới hạn token cho mỗi request
        self.progress_callback = None  # Callback để cập nhật progress
        self.is_processing = False  # Flag để kiểm tra trạng thá<PERSON> xử lý

        # Smart sorting settings - Auto-update theo thời gian thực
        self.current_season = self.get_current_season()
        self.current_month = datetime.datetime.now().month
        self.current_weekday = datetime.datetime.now().weekday()  # 0=Monday, 6=Sunday
        self.livestream_date = None  # Sẽ được set khi user chọn ngày livestream
        self.category_priorities = self.get_category_priorities()
        self.timeslot_priorities = self.get_timeslot_priorities()
        self.weekday_priorities = self.get_weekday_priorities()
        


    def import_google_sheets(self, sheet_url: str, sheet_name: str = None,
                           end_row: str = None, credentials_data: str = None,
                           auth_type: str = 'oauth') -> List[Dict[str, Any]]:
        """
        Đọc dữ liệu từ Google Sheets với hỗ trợ row range

        Args:
            sheet_url: URL hoặc ID của Google Sheet
            sheet_name: Tên sheet cụ thể (nếu không có sẽ lấy sheet đầu tiên)
            end_row: Dòng cuối cùng cần xử lý (nếu không có sẽ lấy tất cả)
            credentials_data: Base64 encoded credentials (optional)
            auth_type: 'oauth' hoặc 'service'

        Returns:
            List brand đã được đọc
        """
        if not GSHEETS_AVAILABLE:
            raise Exception("Google Sheets integration không khả dụng. Vui lòng cài đặt các package cần thiết.")

        try:
            # Khởi tạo Google Sheets manager với credentials từ file config
            gs_manager = self._get_gsheets_manager(credentials_data, auth_type)

            # Trích xuất sheet ID từ URL nếu cần
            sheet_id = self._extract_sheet_id(sheet_url)

            # Mở Google Sheet
            spreadsheet = gs_manager.open_by_key(sheet_id)

            # Chọn worksheet - ưu tiên tìm "brandlist"
            worksheet = self._select_worksheet(spreadsheet, sheet_name)

            # Lấy dữ liệu với row range
            all_values = self._get_sheet_data_with_range(worksheet, end_row)

            if not all_values or len(all_values) < 2:
                raise Exception("Sheet không có đủ dữ liệu (cần ít nhất header + 1 dòng dữ liệu)")

            # Chuyển đổi thành DataFrame
            headers = all_values[0]
            data_rows = all_values[1:]
            df = pd.DataFrame(data_rows, columns=headers)

            # Xử lý dữ liệu với mapping cột tối ưu
            return self._process_dataframe(df, source="Google Sheets")

        except Exception as e:
            raise Exception(f"Lỗi khi đọc Google Sheets: {str(e)}")

    def _get_gsheets_manager(self, credentials_data: str = None, auth_type: str = 'oauth'):
        """Khởi tạo Google Sheets manager với credentials tích hợp sẵn"""
        if credentials_data:
            return GoogleSheetManager(auth_type=auth_type, credentials_data=credentials_data)
        else:
            # Sử dụng credentials tích hợp sẵn
            integrated_credentials = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
            return GoogleSheetManager(auth_type='oauth', credentials_data=integrated_credentials)

    def _select_worksheet(self, spreadsheet, sheet_name: str = None):
        """Chọn worksheet với ưu tiên tìm 'brandlist'"""
        if sheet_name:
            try:
                return spreadsheet.worksheet(sheet_name)
            except:
                pass

        # Tìm sheet có tên chứa "brandlist" (case-insensitive, ignore spaces)
        for worksheet in spreadsheet.worksheets():
            title_normalized = worksheet.title.lower().replace(' ', '')
            if 'brandlist' in title_normalized:
                return worksheet

        # Nếu không tìm thấy, dùng sheet đầu tiên
        return spreadsheet.sheet1

    def _get_sheet_data_with_range(self, worksheet, end_row: str = None):
        """Lấy dữ liệu từ sheet với row range"""
        if not end_row or not end_row.strip():
            return worksheet.get_all_values()

        try:
            end_row_num = int(end_row.strip())
            if end_row_num < 2:
                return worksheet.get_all_values()

            # Lấy dữ liệu từ A1 đến cột cuối và dòng end_row
            range_str = f"A1:Z{end_row_num}"
            return worksheet.get(range_str)
        except (ValueError, Exception):
            # Nếu có lỗi, lấy tất cả dữ liệu
            return worksheet.get_all_values()

    def _extract_sheet_id(self, sheet_url: str) -> str:
        """Trích xuất Sheet ID từ URL Google Sheets"""
        if '/spreadsheets/d/' in sheet_url:
            # URL format: https://docs.google.com/spreadsheets/d/SHEET_ID/edit#gid=0
            start = sheet_url.find('/spreadsheets/d/') + len('/spreadsheets/d/')
            end = sheet_url.find('/', start)
            if end == -1:
                end = sheet_url.find('#', start)
            if end == -1:
                end = len(sheet_url)
            return sheet_url[start:end]
        else:
            # Giả sử đã là Sheet ID
            return sheet_url

    def _process_dataframe(self, df: pd.DataFrame, source: str = "Excel") -> List[Dict[str, Any]]:
        """
        Xử lý DataFrame chung cho cả Excel và Google Sheets
        """
        # Làm sạch tên cột (loại bỏ khoảng trắng thừa)
        df.columns = df.columns.str.strip()

        # Tìm kiếm cột một cách linh hoạt
        column_mapping = self._find_columns(df.columns.tolist())

        # Kiểm tra các cột bắt buộc
        required_fields = ['brand', 'category', 'sku_review', 'gmv']
        missing_fields = [field for field in required_fields if field not in column_mapping]

        if missing_fields:
            available_columns = list(df.columns)
            raise ValueError(
                f"Không tìm thấy các cột bắt buộc: {missing_fields}\n"
                f"Các cột có sẵn: {available_columns}\n"
                f"Cần có các cột: Brand, Category, SKU Review, GMV (hoặc tương tự)"
            )

        # Rename cột theo mapping
        df_renamed = df.rename(columns={v: k for k, v in column_mapping.items()})

        # Làm sạch dữ liệu
        df_clean = df_renamed.dropna(subset=['brand', 'category'])
        df_clean = df_clean.drop_duplicates(subset=['brand'])

        # Chuyển đổi thành list dict
        self.brands = []
        self.categories = set()

        for _, row in df_clean.iterrows():
            # Xử lý dữ liệu an toàn
            brand = {
                "name": str(row['brand']).strip(),
                "category": str(row['category']).strip(),
                "sku_review": self._safe_int_convert(row['sku_review']),
                "gmv": self._safe_float_convert(row['gmv']),
                "assigned": False
            }

            # Thêm thông tin Type nếu có
            if 'type' in column_mapping and 'type' in df_renamed.columns:
                brand["type"] = str(row.get('type', '')).strip()
            else:
                brand["type"] = ""

            # Bỏ qua brand có tên trống
            if brand["name"] and brand["name"].lower() not in ['', 'nan', 'none']:
                self.brands.append(brand)
                self.categories.add(brand["category"])

        # Giữ nguyên thứ tự trong sheet (không sắp xếp lại)
        # self.brands.sort(key=lambda x: x['gmv'], reverse=True)  # Commented out

        print(f"Đã nhập {len(self.brands)} brands từ {len(self.categories)} danh mục ({source}) - Giữ nguyên thứ tự sheet")
        return self.brands

    def _find_columns(self, available_columns: List[str]) -> Dict[str, str]:
        """
        Tìm kiếm cột một cách linh hoạt dựa trên tên tương tự
        """
        return self._find_columns_optimized(available_columns)

    def _find_columns_optimized(self, available_columns: List[str]) -> Dict[str, str]:
        """
        Tìm kiếm cột tối ưu theo yêu cầu: Brand code, Cate, Brand, Type, SKU Review, AVERAGE GMV
        Chuyển về lowercase, không khoảng trắng để mapping dễ dàng
        """
        column_mapping = {}

        # Chuẩn hóa tên cột có sẵn
        normalized_columns = {}
        for col in available_columns:
            normalized = col.lower().replace(' ', '').replace('_', '').replace('-', '')
            normalized_columns[normalized] = col

        # Định nghĩa mapping theo yêu cầu cụ thể - ưu tiên exact match
        target_mappings = {
            'brand': ['brandcode', 'brand', 'brandname', 'tenbrand', 'thuonghieu'],
            'category': ['cate', 'category', 'danhmuc', 'loai', 'phanloai'],
            'type': ['type', 'loai', 'kieuloai', 'phanloai'],
            'sku_review': ['skureview', 'sku', 'review', 'soreview', 'sosku'],
            'gmv': ['averagegmv', 'average_gmv', 'avg_gmv', 'gmv', 'doanhso', 'doanhthu', 'revenue', 'giatri']
        }

        # Tìm kiếm cho từng field
        for field, patterns in target_mappings.items():
            found_column = None

            # Tìm exact match trước
            for pattern in patterns:
                if pattern in normalized_columns:
                    found_column = normalized_columns[pattern]
                    break

            # Nếu không tìm thấy exact match, tìm contains
            if not found_column:
                for pattern in patterns:
                    for norm_col, orig_col in normalized_columns.items():
                        if pattern in norm_col or norm_col in pattern:
                            found_column = orig_col
                            break
                    if found_column:
                        break

            if found_column:
                column_mapping[field] = found_column

        return column_mapping

    def _safe_int_convert(self, value) -> int:
        """Chuyển đổi giá trị thành int một cách an toàn"""
        try:
            if pd.isna(value) or value == '' or str(value).lower() in ['nan', 'none']:
                return 0
            # Loại bỏ dấu phẩy và khoảng trắng
            clean_value = str(value).replace(',', '').replace(' ', '')
            return int(float(clean_value))
        except (ValueError, TypeError):
            return 0

    def _safe_float_convert(self, value) -> float:
        """Chuyển đổi giá trị thành float một cách an toàn"""
        try:
            if pd.isna(value) or value == '' or str(value).lower() in ['nan', 'none', 'null']:
                return 0.0

            # Chuyển thành string để xử lý
            str_value = str(value).strip()

            # Loại bỏ các ký tự không phải số
            # Giữ lại dấu chấm, dấu phẩy, dấu trừ
            clean_value = str_value.replace(',', '').replace(' ', '').replace('$', '').replace('₫', '')

            # Xử lý trường hợp có dấu phẩy làm dấu thập phân (định dạng Châu Âu)
            if ',' in str_value and '.' not in str_value:
                clean_value = clean_value.replace(',', '.')

            result = float(clean_value)
            return result

        except (ValueError, TypeError):
            return 0.0

    def get_available_sheets(self, sheet_url: str, credentials_data: str = None,
                           auth_type: str = 'oauth') -> List[str]:
        """
        Lấy danh sách các sheet có sẵn trong Google Spreadsheet
        """
        if not GSHEETS_AVAILABLE:
            raise Exception("Google Sheets integration không khả dụng.")

        try:
            # Khởi tạo Google Sheets manager
            gs_manager = self._get_gsheets_manager(credentials_data, auth_type)

            # Trích xuất sheet ID từ URL
            sheet_id = self._extract_sheet_id(sheet_url)

            # Mở Google Sheet
            spreadsheet = gs_manager.open_by_key(sheet_id)

            # Lấy danh sách tên các worksheet
            sheet_names = [worksheet.title for worksheet in spreadsheet.worksheets()]

            return sheet_names

        except Exception as e:
            raise Exception(f"Lỗi khi lấy danh sách sheets: {str(e)}")



    def preview_google_sheets_data(self, sheet_url: str, sheet_name: str = None,
                                 end_row: str = None, credentials_data: str = None,
                                 auth_type: str = 'oauth', max_rows: int = 10) -> Dict[str, Any]:
        """
        Preview dữ liệu từ Google Sheets để kiểm tra trước khi import
        """
        if not GSHEETS_AVAILABLE:
            raise Exception("Google Sheets integration không khả dụng.")

        try:
            # Khởi tạo Google Sheets manager
            gs_manager = self._get_gsheets_manager(credentials_data, auth_type)

            # Trích xuất sheet ID từ URL
            sheet_id = self._extract_sheet_id(sheet_url)

            # Mở Google Sheet
            spreadsheet = gs_manager.open_by_key(sheet_id)

            # Chọn worksheet với ưu tiên brandlist
            worksheet = self._select_worksheet(spreadsheet, sheet_name)

            # Lấy dữ liệu với row range
            all_values = self._get_sheet_data_with_range(worksheet, end_row)

            if not all_values:
                return {"error": "Sheet không có dữ liệu"}

            # Tạo preview
            headers = all_values[0]
            total_data_rows = len(all_values) - 1  # Trừ header
            preview_data = all_values[1:min(max_rows+1, len(all_values))]

            # Phân tích cột với mapping tối ưu
            column_mapping = self._find_columns_optimized(headers)

            return {
                "headers": headers,
                "preview_data": preview_data,
                "total_rows": total_data_rows,
                "column_mapping": column_mapping,
                "sheet_name": worksheet.title,
                "end_row_specified": end_row,
                "data_range": f"Dòng 2 → {end_row or 'cuối'}"
            }

        except Exception as e:
            return {"error": f"Lỗi khi preview: {str(e)}"}


    
    def add_timeslot(self, name: str, start_time: str, end_time: str,
                    allowed_categories: List[str], high_traffic: bool,
                    min_sku: int, exclusive: bool = False,
                    exclusive_brand: str = None, exclusive_duration: int = 60,
                    category_quotas: Dict[str, int] = None) -> Dict[str, Any]:
        """
        Thêm một khung giờ mới

        Args:
            name: Tên khung giờ
            start_time: Giờ bắt đầu (HH:MM)
            end_time: Giờ kết thúc (HH:MM)
            allowed_categories: Danh sách các danh mục được phép trong khung giờ này
            high_traffic: Có phải khung giờ lưu lượng cao không
            min_sku: Số lượng SKU tối thiểu cần có
            exclusive: Có phải khung giờ độc quyền không
            exclusive_brand: Tên brand độc quyền (nếu có)
            exclusive_duration: Thời lượng khung giờ độc quyền (phút)
            category_quotas: Dict quota SKU review theo từng category (VD: {"ELHA": 15, "HB": 9})

        Returns:
            Dict thông tin khung giờ đã tạo
        """
        # Nếu không có category_quotas, tạo từ allowed_categories và min_sku
        if category_quotas is None:
            category_quotas = {}
            if allowed_categories and min_sku > 0:
                # Phân bổ đều min_sku cho các categories
                quota_per_cat = min_sku // len(allowed_categories)
                remainder = min_sku % len(allowed_categories)
                for i, cat in enumerate(allowed_categories):
                    category_quotas[cat] = quota_per_cat + (1 if i < remainder else 0)

        timeslot = {
            "id": len(self.timeslots),
            "name": name,
            "start_time": start_time,
            "end_time": end_time,
            "allowed_categories": allowed_categories,
            "high_traffic": high_traffic,
            "min_sku": min_sku,
            "category_quotas": category_quotas or {},  # Quota theo từng category
            "exclusive": exclusive,
            "exclusive_brand": exclusive_brand,
            "exclusive_duration": exclusive_duration,
            "brands": []  # Danh sách các brand được phân vào khung giờ này
        }

        self.timeslots.append(timeslot)
        return timeslot
    
    def get_all_categories(self) -> List[str]:
        """Lấy danh sách tất cả các danh mục hiện có"""
        return list(self.categories)
    
    def get_all_timeslots(self) -> List[Dict[str, Any]]:
        """Lấy danh sách tất cả các khung giờ"""
        return self.timeslots
    
    def calculate_avg_gmv(self, brands: List[Dict[str, Any]]) -> float:
        """Tính trung bình GMV của một danh sách brand"""
        if not brands:
            return 0
        total_gmv = sum(brand["gmv"] for brand in brands)
        return total_gmv / len(brands)

    def calculate_sum_gmv(self, brands: List[Dict[str, Any]]) -> float:
        """Tính tổng GMV của một danh sách brand (SUM OF AVERAGE GMV)"""
        if not brands:
            return 0
        return sum(brand["gmv"] for brand in brands)
    
    def calculate_total_sku_review(self, brands: List[Dict[str, Any]]) -> int:
        """Tính tổng số SKU Review của một danh sách brand"""
        return sum(brand["sku_review"] for brand in brands)

    def set_progress_callback(self, callback: Callable[[int, str], None]):
        """Đặt callback function để cập nhật progress"""
        self.progress_callback = callback

    def update_progress(self, percentage: int, message: str):
        """Cập nhật progress nếu có callback"""
        if self.progress_callback:
            self.progress_callback(percentage, message)

    def chunk_brands(self, brands: List[Dict[str, Any]], chunk_size: int = None) -> List[List[Dict[str, Any]]]:
        """
        Chia danh sách brands thành các chunk nhỏ hơn

        Args:
            brands: Danh sách brands cần chia
            chunk_size: Kích thước mỗi chunk (mặc định sử dụng self.chunk_size)

        Returns:
            List các chunk brands
        """
        if chunk_size is None:
            chunk_size = self.chunk_size

        chunks = []
        for i in range(0, len(brands), chunk_size):
            chunk = brands[i:i + chunk_size]
            chunks.append(chunk)

        return chunks

    def estimate_token_count(self, text: str) -> int:
        """
        Ước tính số token trong text (rough estimation)
        1 token ≈ 4 characters cho tiếng Việt
        """
        return len(text) // 3

    def filter_brands_by_category(self, brands: List[Dict[str, Any]], allowed_categories: List[str]) -> List[Dict[str, Any]]:
        """Lọc brands theo danh mục được phép"""
        return [brand for brand in brands if brand["category"] in allowed_categories]

    def set_livestream_date(self, date_str: str = None):
        """Set ngày livestream để tối ưu thuật toán theo ngày cụ thể"""
        if date_str:
            try:
                # Parse date string (format: YYYY-MM-DD hoặc DD/MM/YYYY)
                if '/' in date_str:
                    day, month, year = date_str.split('/')
                    livestream_date = datetime.datetime(int(year), int(month), int(day))
                else:
                    livestream_date = datetime.datetime.strptime(date_str, '%Y-%m-%d')

                self.livestream_date = livestream_date
                self.current_month = livestream_date.month
                self.current_weekday = livestream_date.weekday()
                self.current_season = self.get_season_for_month(livestream_date.month)

                # Refresh priorities với thông tin mới
                self.category_priorities = self.get_category_priorities()
                self.timeslot_priorities = self.get_timeslot_priorities()
                self.weekday_priorities = self.get_weekday_priorities()

                print(f"📅 Đã set ngày livestream: {date_str} ({self.get_weekday_name()}, {self.current_season})")

            except Exception as e:
                print(f"❌ Lỗi parse ngày: {e}. Sử dụng ngày hiện tại.")
                self.reset_to_current_date()
        else:
            self.reset_to_current_date()

    def reset_to_current_date(self):
        """Reset về ngày hiện tại"""
        now = datetime.datetime.now()
        self.livestream_date = now
        self.current_month = now.month
        self.current_weekday = now.weekday()
        self.current_season = self.get_current_season()

        # Refresh priorities
        self.category_priorities = self.get_category_priorities()
        self.timeslot_priorities = self.get_timeslot_priorities()
        self.weekday_priorities = self.get_weekday_priorities()

    def get_current_season(self) -> str:
        """Xác định mùa hiện tại (auto-update)"""
        month = datetime.datetime.now().month
        return self.get_season_for_month(month)

    def get_season_for_month(self, month: int) -> str:
        """Xác định mùa cho tháng cụ thể"""
        if month in [12, 1, 2]:
            return "winter"  # Mùa đông
        elif month in [3, 4, 5]:
            return "spring"  # Mùa xuân
        elif month in [6, 7, 8]:
            return "summer"  # Mùa hè
        else:
            return "autumn"  # Mùa thu

    def get_weekday_name(self) -> str:
        """Lấy tên thứ trong tuần"""
        weekdays = ["Thứ 2", "Thứ 3", "Thứ 4", "Thứ 5", "Thứ 6", "Thứ 7", "Chủ nhật"]
        return weekdays[self.current_weekday]

    def get_category_priorities(self) -> Dict[str, Dict[str, float]]:
        """
        Định nghĩa độ ưu tiên của category theo mùa - Shopee Vietnam context
        """
        return {
            "winter": {  # Mùa đông VN (khô, mát)
                "thời trang": 1.3,  # Áo khoác, quần dài (trend mạnh VN)
                "làm đẹp": 1.2,     # Chăm sóc da khô, son môi
                "đồ ăn": 1.4,       # Đồ ăn nóng, lẩu, trà (văn hóa VN)
                "gia dụng": 1.1,    # Chăn ấm, máy sưởi
                "điện tử": 1.0,     # Ổn định
                "thể thao": 0.9,    # Giảm do thời tiết
                "mẹ và bé": 1.1     # Đồ ấm cho bé
            },
            "spring": {  # Mùa xuân VN (ẩm, nóng dần)
                "thời trang": 1.4,  # Thời trang xuân, đồ mỏng (hot trend VN)
                "làm đẹp": 1.3,     # Skincare chống ẩm, makeup
                "đồ ăn": 1.1,       # Đồ uống mát, trái cây
                "gia dụng": 1.2,    # Dọn dẹp nhà (tết, xuân)
                "điện tử": 1.0,     # Ổn định
                "thể thao": 1.3,    # Tập luyện sau tết
                "mẹ và bé": 1.0     # Bình thường
            },
            "summer": {  # Mùa hè VN (nóng, ẩm)
                "thời trang": 1.2,  # Đồ mát, váy, áo thun
                "làm đẹp": 1.5,     # Chống nắng, kem dưỡng (cực hot VN)
                "đồ ăn": 0.8,       # Ít đồ nóng, nhiều đồ uống
                "gia dụng": 1.4,    # Quạt, điều hòa (essential VN)
                "điện tử": 1.1,     # Gadgets mát mẻ
                "thể thao": 0.9,    # Giảm do nóng
                "mẹ và bé": 1.2     # Đồ chống nắng cho bé
            },
            "autumn": {  # Mùa thu VN (mát, khô dần)
                "thời trang": 1.3,  # Thời trang thu, layer
                "làm đẹp": 1.1,     # Chăm sóc da chuyển mùa
                "đồ ăn": 1.2,       # Đồ ăn ấm dần
                "gia dụng": 1.0,    # Bình thường
                "điện tử": 1.3,     # Mua sắm cuối năm (11.11, 12.12)
                "thể thao": 1.2,    # Thời tiết dễ chịu
                "mẹ và bé": 1.0     # Bình thường
            }
        }

    def get_weekday_priorities(self) -> Dict[str, Dict[str, float]]:
        """
        Ưu tiên categories theo ngày trong tuần (Shopee Vietnam behavior)
        """
        return {
            "weekday": {  # Thứ 2-6: Working days
                "thời trang": 1.1,  # Office wear, casual
                "làm đẹp": 1.3,     # Skincare routine, makeup cho đi làm
                "đồ ăn": 1.2,       # Đồ ăn nhanh, healthy food
                "gia dụng": 1.0,    # Bình thường
                "điện tử": 1.4,     # Gadgets cho work, productivity
                "thể thao": 0.9,    # Ít thời gian tập
                "mẹ và bé": 1.1     # Đồ dùng hàng ngày cho bé
            },
            "friday": {  # Thứ 6: Chuẩn bị cuối tuần
                "thời trang": 1.4,  # Outfit cuối tuần, party wear
                "làm đẹp": 1.5,     # Makeup, skincare cho weekend
                "đồ ăn": 1.3,       # Đồ ăn cho weekend, snacks
                "gia dụng": 1.1,    # Chuẩn bị dọn dẹp
                "điện tử": 1.2,     # Entertainment gadgets
                "thể thao": 1.1,    # Chuẩn bị tập cuối tuần
                "mẹ và bé": 1.0     # Bình thường
            },
            "weekend": {  # Thứ 7, Chủ nhật: Thời gian rảnh
                "thời trang": 1.5,  # Shopping thời trang, trendy items
                "làm đẹp": 1.4,     # Self-care, beauty treatments
                "đồ ăn": 1.6,       # Đồ ăn ngon, treats, family meals
                "gia dụng": 1.3,    # Home improvement, dọn dẹp nhà
                "điện tử": 1.3,     # Gaming, entertainment
                "thể thao": 1.5,    # Thời gian tập luyện, outdoor
                "mẹ và bé": 1.4     # Thời gian với gia đình, đồ chơi
            }
        }

    def get_current_weekday_type(self) -> str:
        """Xác định loại ngày trong tuần"""
        if self.current_weekday == 4:  # Friday (0=Monday)
            return "friday"
        elif self.current_weekday in [5, 6]:  # Saturday, Sunday
            return "weekend"
        else:  # Monday-Thursday
            return "weekday"

    def get_timeslot_priorities(self) -> Dict[str, Dict[str, float]]:
        """
        Định nghĩa độ ưu tiên của category theo khung giờ - Shopee Vietnam behavior
        """
        return {
            "morning": {  # 6-11h - Sáng sớm VN
                "đồ ăn": 1.6,       # Đồ ăn sáng, cà phê (văn hóa VN)
                "làm đẹp": 1.3,     # Skincare routine sáng
                "thời trang": 0.9,  # Ít shopping sáng sớm
                "gia dụng": 1.2,    # Đồ nhà bếp, nấu nướng
                "điện tử": 0.7,     # Rất ít
                "thể thao": 1.4,    # Tập luyện sáng (popular VN)
                "mẹ và bé": 1.3     # Chuẩn bị cho bé đi học
            },
            "noon": {  # 11-14h - Lunch break shopping (peak Shopee VN)
                "thời trang": 1.5,  # Shopping lunch break (hot trend VN)
                "làm đẹp": 1.4,     # Makeup, skincare (office workers)
                "đồ ăn": 1.3,       # Đồ ăn trưa, snack
                "gia dụng": 1.1,    # Đồ dùng văn phòng
                "điện tử": 1.0,     # Accessories, phone case
                "thể thao": 0.8,    # Ít
                "mẹ và bé": 1.0     # Bình thường
            },
            "afternoon": {  # 14-18h - Afternoon browsing
                "gia dụng": 1.4,    # Đồ gia đình, nội thất
                "mẹ và bé": 1.5,    # Mẹ shopping cho bé (peak time)
                "thời trang": 1.2,  # Browse thời trang
                "làm đẹp": 1.1,     # Skincare research
                "đồ ăn": 1.0,       # Snack, đồ uống
                "điện tử": 1.1,     # Research gadgets
                "thể thao": 1.0     # Đồ tập
            },
            "evening": {  # 18-22h - Prime time Shopee VN
                "điện tử": 1.5,     # Prime time cho tech (sau work)
                "thời trang": 1.3,  # Fashion browsing tối
                "gia dụng": 1.3,    # Đồ gia dụng (plan cho ngày mai)
                "đồ ăn": 1.4,       # Đồ ăn tối, đặc sản
                "làm đẹp": 1.2,     # Evening skincare
                "thể thao": 1.1,    # Đồ tập tối
                "mẹ và bé": 0.9     # Bé đi ngủ sớm
            }
        }

    def get_timeslot_type(self, start_time: str) -> str:
        """Xác định loại khung giờ dựa trên thời gian bắt đầu"""
        try:
            hour = int(start_time.split(':')[0])
            if 6 <= hour < 11:
                return "morning"
            elif 11 <= hour < 14:
                return "noon"
            elif 14 <= hour < 18:
                return "afternoon"
            else:
                return "evening"
        except:
            return "evening"  # Default

    def calculate_brand_priority(self, brand: Dict[str, Any], timeslot: Dict[str, Any]) -> float:
        """
        Tính độ ưu tiên của brand cho một timeslot cụ thể
        """
        base_priority = brand["gmv"] / 1000000  # Normalize GMV

        # Bonus theo mùa (auto-update)
        season_bonus = self.category_priorities.get(self.current_season, {}).get(
            brand["category"].lower(), 1.0
        )

        # Bonus theo ngày trong tuần
        weekday_type = self.get_current_weekday_type()
        weekday_bonus = self.weekday_priorities.get(weekday_type, {}).get(
            brand["category"].lower(), 1.0
        )

        # Bonus theo khung giờ
        timeslot_type = self.get_timeslot_type(timeslot["start_time"])
        time_bonus = self.timeslot_priorities.get(timeslot_type, {}).get(
            brand["category"].lower(), 1.0
        )

        # Bonus high traffic
        traffic_bonus = 1.2 if timeslot.get("high_traffic", False) else 1.0

        # SKU review bonus
        sku_bonus = min(brand["sku_review"] / 100, 2.0)  # Cap at 2x

        final_priority = base_priority * season_bonus * weekday_bonus * time_bonus * traffic_bonus * (1 + sku_bonus)

        return final_priority
    
    def prepare_gpt_prompt_for_chunk(self, brands_chunk: List[Dict[str, Any]],
                                   available_timeslots: List[Dict[str, Any]],
                                   max_gmv_diff: float,
                                   existing_assignments: Dict[str, List[Dict[str, Any]]] = None) -> str:
        """
        Chuẩn bị prompt tối ưu cho một chunk brands

        Args:
            brands_chunk: Chunk brands cần sắp xếp
            available_timeslots: Danh sách khung giờ còn có thể nhận thêm brands
            max_gmv_diff: Chênh lệch GMV tối đa được phép
            existing_assignments: Các phân bổ đã có (để tham khảo)

        Returns:
            Prompt string tối ưu
        """
        # Tính toán thống kê hiện tại
        current_stats = {}
        if existing_assignments:
            for ts_name, brands in existing_assignments.items():
                current_stats[ts_name] = {
                    "count": len(brands),
                    "avg_gmv": self.calculate_avg_gmv(brands),
                    "total_sku": self.calculate_total_sku_review(brands)
                }

        # Chuẩn bị thông tin brands với Type
        brands_with_type = []
        for brand in brands_chunk:
            brand_info = {
                "name": brand["name"],
                "category": brand["category"],
                "type": brand.get("type", ""),
                "sku_review": brand["sku_review"],
                "gmv": brand["gmv"]
            }
            brands_with_type.append(brand_info)

        # Tính target SUM GMV cho cân bằng với high/normal traffic rules
        target_sum_gmv_info = ""
        if existing_assignments:
            # Phân loại theo traffic type
            high_traffic_gmvs = []
            normal_traffic_gmvs = []

            for ts_name, brands in existing_assignments.items():
                if brands:
                    sum_gmv = self.calculate_sum_gmv(brands)
                    # Tìm timeslot info
                    ts_info = next((ts for ts in available_timeslots if ts["name"] == ts_name), None)
                    if ts_info:
                        if ts_info.get("high_traffic", False):
                            high_traffic_gmvs.append(sum_gmv)
                        else:
                            normal_traffic_gmvs.append(sum_gmv)

            if normal_traffic_gmvs:
                avg_normal_gmv = sum(normal_traffic_gmvs) / len(normal_traffic_gmvs)
                target_sum_gmv_info = f"Normal Traffic Target: {avg_normal_gmv * (1-max_gmv_diff):,.0f} - {avg_normal_gmv * (1+max_gmv_diff):,.0f}"
                target_sum_gmv_info += f" | High Traffic Target: {avg_normal_gmv * 1.5:,.0f} - {avg_normal_gmv * 1.7:,.0f} (50-70% higher)"

        # Chuẩn bị quota requirements nghiêm ngặt
        quota_requirements = []
        for ts in available_timeslots:
            if "category_quotas" in ts and ts["category_quotas"]:
                quota_desc = []
                for cat, quota in ts["category_quotas"].items():
                    if quota > 0:
                        quota_desc.append(f"{cat}={quota}")
                quota_requirements.append(f"{ts['name']}: {'+'.join(quota_desc)} SKU reviews")
            else:
                quota_requirements.append(f"{ts['name']}: {ts.get('min_sku', 0)} SKU reviews total")

        prompt = f"""SHOPEE VIETNAM BRAND ALLOCATION - STRICT QUOTA ENFORCEMENT

BRANDS TO ALLOCATE ({len(brands_chunk)} brands):
{json.dumps(brands_with_type, indent=2, ensure_ascii=False)}

TIMESLOTS WITH EXACT QUOTA REQUIREMENTS:
{chr(10).join(quota_requirements)}

CURRENT ALLOCATIONS:
{json.dumps(current_stats, indent=2, ensure_ascii=False)}

{target_sum_gmv_info}

ABSOLUTE RULES (NO EXCEPTIONS):
1. 🚨 QUOTA ZERO TOLERANCE: Each timeslot must have EXACTLY the specified SKU reviews per category
2. 💰 SUM GMV BALANCE: Total GMV per timeslot within ±{max_gmv_diff*100:.0f}% of each other
3. 🎯 CATEGORY RESTRICTION: Only use brands from allowed categories per timeslot
4. 🔒 NO DUPLICATES: Each brand used only once across all timeslots

RETURN FORMAT:
{{
  "timeslot_name": [
    {{"name": "Brand", "category": "Cat", "type": "Type", "sku_review": X, "gmv": Y}},
    ...
  ]
}}

VALIDATION REQUIRED:
- Count SKU reviews per category per timeslot = EXACT match
- SUM GMV per timeslot within specified range
- No brand appears twice"""

        return prompt

    def prepare_gpt_prompt(self, max_gmv_diff: float) -> str:
        """
        Chuẩn bị prompt để gửi cho GPT (phương thức cũ, giữ lại để tương thích)
        """
        return self.prepare_gpt_prompt_for_chunk(self.brands, self.timeslots, max_gmv_diff)
    
    def process_chunk_with_gpt(self, client: openai.OpenAI, brands_chunk: List[Dict[str, Any]],
                             available_timeslots: List[Dict[str, Any]], max_gmv_diff: float,
                             existing_assignments: Dict[str, List[Dict[str, Any]]] = None) -> Dict[str, List[Dict[str, Any]]]:
        """
        Xử lý một chunk brands với GPT

        Args:
            client: OpenAI client
            brands_chunk: Chunk brands cần xử lý
            available_timeslots: Khung giờ khả dụng
            max_gmv_diff: Chênh lệch GMV tối đa
            existing_assignments: Phân bổ hiện tại

        Returns:
            Dict kết quả phân bổ cho chunk này
        """
        prompt = self.prepare_gpt_prompt_for_chunk(brands_chunk, available_timeslots, max_gmv_diff, existing_assignments)

        try:
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": self.get_smart_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.0,  # Đặt về 0 để có kết quả deterministic và chính xác
                max_tokens=self.max_tokens_per_request
            )

            content = response.choices[0].message.content.strip()

            # Trích xuất JSON
            json_start = content.find('{')
            json_end = content.rfind('}') + 1

            if json_start >= 0 and json_end > json_start:
                json_str = content[json_start:json_end]
                result = json.loads(json_str)
                return result
            else:
                raise ValueError("Không tìm thấy JSON trong response")

        except json.JSONDecodeError as e:
            raise ValueError(f"Lỗi parse JSON: {str(e)}")
        except Exception as e:
            raise Exception(f"Lỗi API GPT: {str(e)}")

    def sort_brands_optimized(self, api_key: str, max_gmv_diff: float = 0.3) -> Dict[str, List[Dict[str, Any]]]:
        """
        Sắp xếp brands tối ưu với chunking và progress tracking

        Args:
            api_key: OpenAI API key
            max_gmv_diff: Chênh lệch GMV tối đa được phép

        Returns:
            Dict khung giờ và danh sách brand được phân vào đó
        """
        if not self.brands:
            raise ValueError("Chưa có dữ liệu brand nào. Vui lòng nhập dữ liệu trước.")

        if not self.timeslots:
            raise ValueError("Chưa có khung giờ nào. Vui lòng tạo khung giờ trước.")

        self.is_processing = True
        self.update_progress(0, "Bắt đầu xử lý...")

        try:
            client = openai.OpenAI(api_key=api_key)

            # Reset used brands
            self.used_brands = set()

            # Xử lý exclusive timeslots trước
            self.update_progress(5, "Xử lý khung giờ độc quyền...")
            exclusive_results = self.process_exclusive_timeslots()

            # Khởi tạo kết quả với exclusive results
            final_results = {ts["name"]: [] for ts in self.timeslots}
            final_results.update(exclusive_results)

            # Lọc unassigned brands (loại bỏ brands đã dùng trong exclusive)
            unassigned_brands = [b for b in self.brands
                               if b.get("name", b.get("brand", "")) not in self.used_brands]

            # Lọc timeslots không độc quyền cho thuật toán chính
            non_exclusive_timeslots = [ts for ts in self.timeslots if not ts.get("exclusive", False)]

            # Nếu dataset nhỏ (< 80 brands), xử lý một lần
            if len(unassigned_brands) <= 80:
                self.update_progress(20, "Xử lý dataset nhỏ...")
                result = self.process_chunk_with_gpt(client, unassigned_brands, non_exclusive_timeslots, max_gmv_diff)
                # Chỉ cập nhật cho non-exclusive timeslots
                for ts_name, brands in result.items():
                    if ts_name in [ts["name"] for ts in non_exclusive_timeslots]:
                        final_results[ts_name] = brands
                self.update_progress(90, "Hoàn thành xử lý...")
            else:
                # Chia thành chunks và xử lý tuần tự
                chunks = self.chunk_brands(unassigned_brands, self.chunk_size)
                total_chunks = len(chunks)

                self.update_progress(10, f"Chia thành {total_chunks} chunks...")

                for i, chunk in enumerate(chunks):
                    progress = 10 + (i * 70 // total_chunks)
                    self.update_progress(progress, f"Xử lý chunk {i+1}/{total_chunks}...")

                    # Lọc brands chưa được assign
                    remaining_chunk = [b for b in chunk if not b.get("assigned", False)]
                    if not remaining_chunk:
                        continue

                    # Xử lý chunk với non-exclusive timeslots
                    chunk_result = self.process_chunk_with_gpt(
                        client, remaining_chunk, non_exclusive_timeslots, max_gmv_diff, final_results
                    )

                    # Merge kết quả (chỉ cho non-exclusive timeslots)
                    for ts_name, brands in chunk_result.items():
                        if ts_name in final_results and ts_name in [ts["name"] for ts in non_exclusive_timeslots]:
                            final_results[ts_name].extend(brands)

                    # Đánh dấu brands đã được assign và cập nhật used_brands
                    assigned_names = set()
                    for brands in chunk_result.values():
                        for brand in brands:
                            brand_name = brand["name"]
                            if brand_name in self.used_brands:
                                print(f"🚨 Duplicate detected in chunk: {brand_name}")
                            else:
                                assigned_names.add(brand_name)
                                self.used_brands.add(brand_name)

                    for brand in self.brands:
                        if brand["name"] in assigned_names:
                            brand["assigned"] = True

                    print(f"✅ Chunk {i+1}/{total_chunks}: Assigned {len(assigned_names)} brands, total used: {len(self.used_brands)}")

                    # Delay nhỏ để tránh rate limit
                    time.sleep(0.5)

            # Lưu kết quả cuối cùng
            self.results = final_results
            self.update_progress(95, "Cập nhật trạng thái...")

            # Cập nhật trạng thái brands
            used_brands = set()
            for brands in final_results.values():
                for brand in brands:
                    used_brands.add(brand["name"])

            for brand in self.brands:
                brand["assigned"] = brand["name"] in used_brands

            self.used_brands = used_brands

            # Thống kê cuối bao gồm exclusive timeslots
            total_assigned = sum(len(brands) for brands in final_results.values())
            total_gmv = sum(sum(b["gmv"] for b in brands) for brands in final_results.values())
            exclusive_count = len(exclusive_results)
            remaining_count = len([b for b in self.brands if not b.get("assigned", False)])

            print(f"\n📊 THỐNG KÊ CUỐI (Optimized):")
            print(f"   - Khung giờ độc quyền: {exclusive_count}")
            print(f"   - Tổng brands đã phân bổ: {total_assigned}/{len(self.brands)}")
            print(f"   - Tổng GMV: {total_gmv:,.0f}")
            print(f"   - Brands chưa phân bổ: {remaining_count}")

            self.update_progress(100, "Hoàn thành!")

            return final_results

        except Exception as e:
            raise Exception(f"Lỗi trong quá trình sắp xếp: {str(e)}")
        finally:
            self.is_processing = False

    def sort_brands_smart_strategy(self, api_key: str, max_gmv_diff: float = 0.3) -> Dict[str, List[Dict[str, Any]]]:
        """
        Thuật toán sắp xếp thông minh với chiến lược toàn cục

        Chiến lược:
        1. Phân tích context (mùa, tháng, thời gian)
        2. Sắp xếp theo độ ưu tiên timeslot
        3. Phân bổ theo category với global view
        4. Cân bằng GMV toàn cục
        """
        if not self.brands:
            raise ValueError("Chưa có dữ liệu brand nào. Vui lòng nhập dữ liệu trước.")

        if not self.timeslots:
            raise ValueError("Chưa có khung giờ nào. Vui lòng tạo khung giờ trước.")

        self.is_processing = True
        self.update_progress(0, "Phân tích context và khởi tạo...")

        try:
            client = openai.OpenAI(api_key=api_key)

            # Reset used brands
            self.used_brands = set()

            # Bước 0: Xử lý exclusive timeslots trước
            self.update_progress(5, "Xử lý khung giờ độc quyền...")
            exclusive_results = self.process_exclusive_timeslots()

            # Bước 1: Phân tích và chuẩn bị dữ liệu
            self.update_progress(10, "Phân tích mùa và context...")
            context_analysis = self.analyze_context()

            # Bước 2: Sắp xếp timeslots theo độ ưu tiên (loại bỏ exclusive)
            self.update_progress(20, "Sắp xếp khung giờ theo độ ưu tiên...")
            all_timeslots = self.sort_timeslots_by_priority()
            sorted_timeslots = [ts for ts in all_timeslots if not ts.get("exclusive", False)]

            # Bước 3: Phân loại brands theo category và priority
            self.update_progress(30, "Phân loại brands theo category...")
            categorized_brands = self.categorize_brands_by_priority()

            # Bước 4: Sắp xếp từng timeslot với global view
            final_results = {ts["name"]: [] for ts in self.timeslots}
            final_results.update(exclusive_results)  # Thêm exclusive results

            # Lọc remaining brands (loại bỏ brands đã dùng trong exclusive)
            remaining_brands = [b for b in self.brands
                              if b.get("name", b.get("brand", "")) not in self.used_brands]

            total_timeslots = len(sorted_timeslots)

            for i, timeslot in enumerate(sorted_timeslots):
                progress = 40 + (i * 40 // total_timeslots)
                self.update_progress(progress, f"Sắp xếp khung giờ: {timeslot['name']}...")

                # Lọc brands phù hợp với timeslot này
                suitable_brands = self.filter_suitable_brands(
                    remaining_brands, timeslot, categorized_brands
                )

                if not suitable_brands:
                    continue

                # Sử dụng GPT để sắp xếp tối ưu cho timeslot này
                timeslot_result = self.optimize_timeslot_with_gpt(
                    client, suitable_brands, timeslot, final_results,
                    context_analysis, max_gmv_diff
                )

                # Cập nhật kết quả
                if timeslot["name"] in timeslot_result:
                    assigned_brands = timeslot_result[timeslot["name"]]
                    final_results[timeslot["name"]] = assigned_brands

                    # QUAN TRỌNG: Cập nhật used_brands ngay lập tức để tránh duplicate
                    assigned_names = {b["name"] for b in assigned_brands}
                    self.used_brands.update(assigned_names)

                    # Loại bỏ brands đã được assign từ remaining_brands
                    remaining_brands = [b for b in remaining_brands if b["name"] not in assigned_names]

                    print(f"✅ Assigned {len(assigned_brands)} brands to {timeslot['name']}, used_brands now: {len(self.used_brands)}")

                # Delay nhỏ để tránh rate limit
                time.sleep(0.3)

            # Bước 5: Xử lý brands còn lại (nếu có)
            self.update_progress(85, "Xử lý brands còn lại...")
            if remaining_brands:
                self.handle_remaining_brands(client, remaining_brands, final_results, max_gmv_diff)

            # Bước 6: Cân bằng GMV cuối cùng
            self.update_progress(95, "Cân bằng GMV cuối cùng...")
            final_results = self.balance_final_gmv(final_results, max_gmv_diff)

            # Lưu kết quả
            self.results = final_results
            self.update_used_brands(final_results)

            # Thống kê cuối bao gồm exclusive timeslots
            total_assigned = sum(len(brands) for brands in final_results.values())
            total_gmv = sum(sum(b["gmv"] for b in brands) for brands in final_results.values())
            exclusive_count = len(exclusive_results)
            remaining_count = len(remaining_brands)

            print(f"\n📊 THỐNG KÊ CUỐI (Smart Strategy):")
            print(f"   - Khung giờ độc quyền: {exclusive_count}")
            print(f"   - Tổng brands đã phân bổ: {total_assigned}/{len(self.brands)}")
            print(f"   - Tổng GMV: {total_gmv:,.0f}")
            print(f"   - Brands chưa phân bổ: {remaining_count}")

            self.update_progress(100, "Hoàn thành!")

            return final_results

        except Exception as e:
            raise Exception(f"Lỗi trong thuật toán sắp xếp thông minh: {str(e)}")
        finally:
            self.is_processing = False

    def sort_brands(self, api_key: str, max_gmv_diff: float = 0.3) -> Dict[str, List[Dict[str, Any]]]:
        """
        Phương thức sắp xếp chính - sử dụng thuật toán thông minh mới
        """
        # Chọn thuật toán dựa trên kích thước dataset
        if len(self.brands) <= 50:
            # Dataset nhỏ: sử dụng thuật toán cũ (đơn giản, nhanh)
            return self.sort_brands_optimized(api_key, max_gmv_diff)
        else:
            # Dataset lớn: sử dụng thuật toán thông minh mới
            return self.sort_brands_smart_strategy(api_key, max_gmv_diff)

    def analyze_context(self) -> Dict[str, Any]:
        """Phân tích context hiện tại cho Shopee Vietnam"""
        context = {
            "season": self.current_season,
            "month": self.current_month,
            "weekday": self.get_weekday_name(),
            "weekday_type": self.get_current_weekday_type(),
            "livestream_date": self.livestream_date.strftime("%Y-%m-%d") if self.livestream_date else "auto",
            "platform": "Shopee Vietnam",
            "market": "Vietnam",
            "climate": "tropical",  # Khí hậu nhiệt đới

            # Shopee Vietnam seasonal events
            "is_holiday_season": self.current_month in [11, 12, 1],  # Black Friday, 12.12, Tết
            "is_summer_sale": self.current_month in [6, 7, 8],       # Shopee Summer Sale
            "is_back_to_school": self.current_month in [8, 9],       # Mùa tựu trường VN
            "is_tet_season": self.current_month in [1, 2],           # Tết Nguyên Đán
            "is_mid_autumn": self.current_month == 9,                # Trung Thu
            "is_womens_day": self.current_month in [3, 10],          # 8/3, 20/10

            # Vietnam shopping behavior
            "peak_shopping_hours": ["11-14", "18-22"],  # Lunch break + Evening
            "price_sensitivity": "high",                # Người Việt nhạy cảm giá
            "review_importance": "very_high",           # Rất coi trọng review
            "mobile_first": True,                       # Shopping chủ yếu mobile

            # Data stats
            "total_brands": len(self.brands),
            "total_categories": len(self.categories),
            "avg_gmv": sum(b["gmv"] for b in self.brands) / len(self.brands) if self.brands else 0,

            # Vietnam market insights
            "top_categories_vn": ["thời trang", "làm đẹp", "gia dụng", "điện tử", "đồ ăn"],
            "cultural_preferences": {
                "family_oriented": True,
                "value_conscious": True,
                "trend_following": True,
                "social_proof_important": True
            }
        }
        return context

    def sort_timeslots_by_priority(self) -> List[Dict[str, Any]]:
        """Sắp xếp timeslots theo độ ưu tiên - High traffic chỉ cao hơn 50-70%, không phải 1000%"""
        def timeslot_priority(ts):
            # Base priority theo thời gian
            time_type = self.get_timeslot_type(ts["start_time"])
            base_priority = {
                "evening": 300,    # Khung giờ vàng
                "noon": 200,       # Khung giờ bạc
                "morning": 100,    # Khung giờ đồng
                "afternoon": 50    # Khung giờ thường
            }.get(time_type, 0)

            # High traffic chỉ bonus 50-70% thay vì 1000%
            if ts.get("high_traffic", False):
                high_traffic_multiplier = 1.6  # 60% bonus
            else:
                high_traffic_multiplier = 1.0

            # Ưu tiên theo min_sku (ít yêu cầu hơn = dễ fill hơn)
            sku_priority = max(0, 100 - ts.get("min_sku", 0))

            return (base_priority + sku_priority) * high_traffic_multiplier

        return sorted(self.timeslots, key=timeslot_priority, reverse=True)

    def process_exclusive_timeslots(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Xử lý các khung giờ độc quyền trước khi chạy thuật toán chính

        Returns:
            Dict kết quả phân bổ cho các exclusive timeslots
        """
        exclusive_results = {}

        for timeslot in self.timeslots:
            if not timeslot.get("exclusive", False):
                continue

            exclusive_brand_name = timeslot.get("exclusive_brand")
            if not exclusive_brand_name:
                continue

            # Tìm brand độc quyền
            exclusive_brand = None
            for brand in self.brands:
                if brand.get("name", brand.get("brand", "")) == exclusive_brand_name:
                    exclusive_brand = brand
                    break

            if not exclusive_brand:
                print(f"⚠️ Không tìm thấy brand '{exclusive_brand_name}' cho khung giờ độc quyền {timeslot['name']}")
                continue

            # Khởi tạo danh sách brands cho timeslot này
            timeslot_brands = [exclusive_brand]

            # Logic phân bổ thêm brands dựa trên thời lượng
            duration = timeslot.get("exclusive_duration", 60)
            exclusive_category = exclusive_brand.get("category", "")

            if duration == 60:
                # 60 phút: Thêm brands cùng category với review = 0
                additional_brands = [
                    b for b in self.brands
                    if (b.get("category", "") == exclusive_category and
                        b.get("sku_review", 0) == 0 and
                        b != exclusive_brand and
                        b.get("name", b.get("brand", "")) not in self.used_brands)
                ]
                # Sắp xếp theo GMV giảm dần để cân bằng
                additional_brands.sort(key=lambda x: x.get("gmv", 0), reverse=True)
                # Thêm một số brands để cân bằng GMV (tối đa 3-5 brands)
                timeslot_brands.extend(additional_brands[:4])

            else:
                # < 60 phút: Thêm brands cùng category với số review theo cấu hình
                # Lấy số review từ cấu hình categories của timeslot
                target_review_count = None
                for cat, sku_count in timeslot.get("allowed_categories", {}).items():
                    if cat.lower() == exclusive_category.lower():
                        # Ước tính số review dựa trên cấu hình (có thể cần điều chỉnh logic này)
                        target_review_count = sku_count  # Tạm thời sử dụng sku_count
                        break

                if target_review_count is None:
                    # Nếu không có cấu hình, sử dụng review count của brand chính
                    target_review_count = exclusive_brand.get("sku_review", 0)

                additional_brands = [
                    b for b in self.brands
                    if (b.get("category", "") == exclusive_category and
                        b.get("sku_review", 0) == target_review_count and
                        b != exclusive_brand and
                        b.get("name", b.get("brand", "")) not in self.used_brands)
                ]
                # Sắp xếp theo GMV giảm dần
                additional_brands.sort(key=lambda x: x.get("gmv", 0), reverse=True)
                # Thêm brands phù hợp với thời lượng (ít hơn cho thời lượng ngắn)
                max_additional = max(1, duration // 15)  # 1 brand cho mỗi 15 phút
                timeslot_brands.extend(additional_brands[:max_additional])

            # Đánh dấu các brands đã sử dụng
            for brand in timeslot_brands:
                brand_name = brand.get("name", brand.get("brand", ""))
                self.used_brands.add(brand_name)

            # Lưu kết quả
            exclusive_results[timeslot["name"]] = timeslot_brands

            print(f"👑 Khung giờ độc quyền {timeslot['name']}: {len(timeslot_brands)} brands")
            print(f"   - Brand chính: {exclusive_brand_name}")
            print(f"   - Thời lượng: {duration} phút")
            print(f"   - Brands bổ sung: {len(timeslot_brands)-1}")

        return exclusive_results

    def categorize_brands_by_priority(self) -> Dict[str, List[Dict[str, Any]]]:
        """Phân loại brands theo category và tính priority cho từng brand"""
        categorized = {}

        for brand in self.brands:
            category = brand["category"]
            if category not in categorized:
                categorized[category] = []

            # Tính priority tổng hợp cho brand này
            brand_with_priority = brand.copy()
            brand_with_priority["global_priority"] = self.calculate_global_brand_priority(brand)
            categorized[category].append(brand_with_priority)

        # Sắp xếp brands trong mỗi category theo priority
        for category in categorized:
            categorized[category].sort(key=lambda x: x["global_priority"], reverse=True)

        return categorized

    def calculate_global_brand_priority(self, brand: Dict[str, Any]) -> float:
        """Tính priority toàn cục của brand (không phụ thuộc timeslot cụ thể)"""
        # Base priority từ GMV
        base_priority = brand["gmv"] / 1000000

        # Bonus theo mùa
        season_bonus = self.category_priorities.get(self.current_season, {}).get(
            brand["category"].lower(), 1.0
        )

        # SKU review bonus
        sku_bonus = min(brand["sku_review"] / 50, 3.0)  # Cap at 3x

        # Bonus theo tháng đặc biệt
        month_bonus = 1.0
        if self.current_month in [11, 12]:  # Black Friday, Noel
            if brand["category"].lower() in ["thời trang", "điện tử", "gia dụng"]:
                month_bonus = 1.3
        elif self.current_month in [6, 7, 8]:  # Mùa hè
            if brand["category"].lower() in ["làm đẹp", "thời trang"]:
                month_bonus = 1.2

        return base_priority * season_bonus * (1 + sku_bonus) * month_bonus

    def filter_suitable_brands(self, remaining_brands: List[Dict[str, Any]],
                             timeslot: Dict[str, Any],
                             categorized_brands: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """Lọc brands phù hợp cho một timeslot cụ thể - LOẠI BỎ USED BRANDS"""
        suitable_brands = []
        allowed_categories = timeslot["allowed_categories"]

        # Lấy brands từ các category được phép
        for category in allowed_categories:
            if category in categorized_brands:
                category_brands = [
                    b for b in categorized_brands[category]
                    if (any(rb["name"] == b["name"] for rb in remaining_brands) and
                        b["name"] not in self.used_brands)  # QUAN TRỌNG: Loại bỏ used brands
                ]
                suitable_brands.extend(category_brands)

        # Sắp xếp theo priority cho timeslot này
        for brand in suitable_brands:
            brand["timeslot_priority"] = self.calculate_brand_priority(brand, timeslot)

        suitable_brands.sort(key=lambda x: x["timeslot_priority"], reverse=True)

        # Giới hạn số lượng để tránh quá tải GPT (tối đa 80 brands)
        filtered_brands = suitable_brands[:80]

        # Debug log để kiểm tra
        print(f"🔍 Filtered brands for {timeslot['name']}: {len(filtered_brands)} brands (excluded {len(self.used_brands)} used brands)")

        return filtered_brands

    def optimize_timeslot_with_gpt(self, client: openai.OpenAI,
                                 suitable_brands: List[Dict[str, Any]],
                                 timeslot: Dict[str, Any],
                                 current_results: Dict[str, List[Dict[str, Any]]],
                                 context: Dict[str, Any],
                                 max_gmv_diff: float) -> Dict[str, List[Dict[str, Any]]]:
        """Sử dụng GPT để tối ưu sắp xếp cho một timeslot cụ thể"""

        # Chuẩn bị brands với thông tin Type đầy đủ
        brands_with_type = []
        for brand in suitable_brands:
            brand_info = brand.copy()
            brand_info["type"] = brand.get("type", "")
            brands_with_type.append(brand_info)

        # Tạo prompt thông minh cho timeslot này
        prompt = self.create_smart_prompt_for_timeslot(
            brands_with_type, timeslot, current_results, context, max_gmv_diff
        )

        try:
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": self.get_smart_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.0,  # Đặt về 0 để có kết quả deterministic và chính xác
                max_tokens=2000
            )

            content = response.choices[0].message.content.strip()

            # Trích xuất JSON
            json_start = content.find('{')
            json_end = content.rfind('}') + 1

            if json_start >= 0 and json_end > json_start:
                json_str = content[json_start:json_end]
                result = json.loads(json_str)

                # VALIDATION: Kiểm tra duplicate brands trong GPT response
                if timeslot["name"] in result:
                    assigned_brands = result[timeslot["name"]]
                    for brand in assigned_brands:
                        brand_name = brand.get("name", "")
                        if brand_name in self.used_brands:
                            print(f"🚨 GPT returned duplicate brand: {brand_name} - Using fallback")
                            return self.fallback_selection(suitable_brands, timeslot)

                return result
            else:
                # Fallback: tự động chọn brands tốt nhất
                return self.fallback_selection(suitable_brands, timeslot)

        except Exception as e:
            print(f"GPT error for timeslot {timeslot['name']}: {e}")
            # Fallback: tự động chọn brands tốt nhất
            return self.fallback_selection(suitable_brands, timeslot)

    def create_smart_prompt_for_timeslot(self, brands: List[Dict[str, Any]],
                                       timeslot: Dict[str, Any],
                                       current_results: Dict[str, List[Dict[str, Any]]],
                                       context: Dict[str, Any],
                                       max_gmv_diff: float) -> str:
        """Tạo prompt thông minh cho một timeslot cụ thể"""

        # Tính thống kê hiện tại
        current_stats = self.calculate_current_stats(current_results)

        # Thông tin context Shopee Vietnam
        context_info = f"""
🇻🇳 SHOPEE VIETNAM CONTEXT:
- Mùa: {context['season']} (tháng {context['month']}) - Khí hậu nhiệt đới VN
- Tổng brands: {context['total_brands']} brands trên Shopee VN
- Avg GMV toàn bộ: {context['avg_gmv']:,.0f} VND
- Mùa lễ hội: {'Có' if context['is_holiday_season'] else 'Không'} (Black Friday, Noel VN)
- Thị trường: Người Việt, price-sensitive, review-focused
- Platform: Shopee Vietnam livestream ecosystem
"""

        # Thông tin timeslot với context Shopee VN
        timeslot_type = self.get_timeslot_type(timeslot["start_time"])
        vietnam_time_context = {
            "morning": "Sáng sớm VN - Cà phê, đồ ăn sáng, chuẩn bị đi làm",
            "noon": "Trưa VN - Lunch break shopping, office workers online",
            "afternoon": "Chiều VN - Mẹ shopping cho gia đình, browse products",
            "evening": "Tối VN - Prime time, sau giờ làm, family time"
        }

        # Tạo thông tin quota chi tiết
        quota_info = ""
        if "category_quotas" in timeslot and timeslot["category_quotas"]:
            quota_details = []
            for category, quota in timeslot["category_quotas"].items():
                if quota > 0:
                    quota_details.append(f"  • {category}: {quota} SKU reviews")
            quota_info = f"""
📋 QUOTA CHI TIẾT THEO CATEGORY:
{chr(10).join(quota_details)}
⚠️  QUAN TRỌNG: Phải tuân thủ CHÍNH XÁC số lượng cho từng category!"""
        else:
            quota_info = f"📋 QUOTA TỔNG: {timeslot['min_sku']} SKU reviews"

        timeslot_info = f"""
📱 SHOPEE VN TIMESLOT: {timeslot['name']}
- Thời gian: {timeslot['start_time']} - {timeslot['end_time']} ({timeslot_type})
- Context VN: {vietnam_time_context.get(timeslot_type, 'Khung giờ đặc biệt')}
- High traffic: {'Có' if timeslot.get('high_traffic', False) else 'Không'} (Shopee peak hours)
{quota_info}
- Danh mục được phép: {', '.join(timeslot['allowed_categories'])}
- User behavior: {'Price-sensitive, quick decisions' if timeslot.get('high_traffic', False) else 'Browse-focused, research mode'}
"""

        # Top brands phù hợp với thông tin Type (chỉ lấy top 30 để tiết kiệm token)
        top_brands_with_type = []
        for brand in brands[:30]:
            brand_info = {
                "name": brand["name"],
                "category": brand["category"],
                "type": brand.get("type", ""),
                "sku_review": brand["sku_review"],
                "gmv": brand["gmv"],
                "timeslot_priority": brand.get("timeslot_priority", 0)
            }
            top_brands_with_type.append(brand_info)

        brands_info = f"""
TOP BRANDS PHÙ HỢP (đã sắp xếp theo độ ưu tiên) - BAO GỒM TYPE:
{json.dumps(top_brands_with_type, indent=2, ensure_ascii=False)}
"""

        # Tính SUM GMV target dựa trên high/normal traffic và max_gmv_diff
        target_sum_gmv = ""
        if current_stats:
            # Phân loại timeslots theo traffic type
            high_traffic_gmvs = []
            normal_traffic_gmvs = []

            for ts_name, stats in current_stats.items():
                # Tìm timeslot info để check high_traffic
                ts_info = next((ts for ts in self.timeslots if ts["name"] == ts_name), None)
                if ts_info:
                    if ts_info.get("high_traffic", False):
                        high_traffic_gmvs.append(stats.get("sum_gmv", 0))
                    else:
                        normal_traffic_gmvs.append(stats.get("sum_gmv", 0))

            # Tính target dựa trên loại traffic của timeslot hiện tại
            is_high_traffic = timeslot.get("high_traffic", False)

            if is_high_traffic and normal_traffic_gmvs:
                # High traffic: 50-70% cao hơn normal traffic
                avg_normal_gmv = sum(normal_traffic_gmvs) / len(normal_traffic_gmvs)
                min_target = avg_normal_gmv * 1.5  # 50% cao hơn
                max_target = avg_normal_gmv * 1.7  # 70% cao hơn
                target_sum_gmv = f"HIGH TRAFFIC - SUM GMV: {min_target:,.0f} - {max_target:,.0f} (50-70% cao hơn normal)"
            elif not is_high_traffic and normal_traffic_gmvs:
                # Normal traffic: cân bằng với nhau theo max_gmv_diff
                avg_normal_gmv = sum(normal_traffic_gmvs) / len(normal_traffic_gmvs)
                min_target = avg_normal_gmv * (1 - max_gmv_diff)
                max_target = avg_normal_gmv * (1 + max_gmv_diff)
                target_sum_gmv = f"NORMAL TRAFFIC - SUM GMV: {min_target:,.0f} - {max_target:,.0f} (±{max_gmv_diff*100:.0f}%)"
            else:
                # Fallback: sử dụng tất cả timeslots
                all_gmvs = [stats.get("sum_gmv", 0) for stats in current_stats.values()]
                if all_gmvs:
                    avg_gmv = sum(all_gmvs) / len(all_gmvs)
                    multiplier = 1.6 if is_high_traffic else 1.0
                    min_target = avg_gmv * multiplier * (1 - max_gmv_diff)
                    max_target = avg_gmv * multiplier * (1 + max_gmv_diff)
                    target_sum_gmv = f"SUM GMV: {min_target:,.0f} - {max_target:,.0f}"

        # Tạo quota requirements nghiêm ngặt
        quota_requirements = ""
        if "category_quotas" in timeslot and timeslot["category_quotas"]:
            quota_requirements = "QUOTA REQUIREMENTS (TUYỆT ĐỐI KHÔNG ĐƯỢC SAI):"
            for category, quota in timeslot["category_quotas"].items():
                if quota > 0:
                    quota_requirements += f"\n- {category}: CHÍNH XÁC {quota} SKU reviews (không 1 ít, không 1 nhiều)"
        else:
            quota_requirements = f"QUOTA REQUIREMENT: CHÍNH XÁC {timeslot['min_sku']} SKU reviews tổng"

        # Tạo danh sách brands đã sử dụng để nhấn mạnh NO DUPLICATES
        used_brands_info = ""
        if current_stats:
            used_brand_names = []
            for ts_name, stats in current_stats.items():
                # Lấy brands từ current results để tránh duplicate
                if ts_name in current_results:
                    for brand in current_results[ts_name]:
                        used_brand_names.append(brand["name"])
            if used_brand_names:
                used_brands_info = f"BRANDS ALREADY USED (DO NOT USE AGAIN): {', '.join(used_brand_names[:10])}{'...' if len(used_brand_names) > 10 else ''}"

        prompt = f"""SHOPEE VIETNAM BRAND SORTING - KHUNG GIỜ: {timeslot['name']}

{quota_requirements}

BRANDS AVAILABLE FOR THIS TIMESLOT:
{json.dumps(top_brands_with_type[:20], indent=2, ensure_ascii=False)}

{used_brands_info}

CURRENT TIMESLOTS SUM GMV:
{json.dumps(current_stats, indent=2, ensure_ascii=False)}

RULES (ABSOLUTE PRIORITY ORDER):
1. 🚨 NO DUPLICATES: NEVER use any brand that appears in "BRANDS ALREADY USED" list
2. 🚨 QUOTA ABSOLUTE: {quota_requirements.replace('QUOTA REQUIREMENTS (TUYỆT ĐỐI KHÔNG ĐƯỢC SAI):', '')}
3. 💰 SUM GMV TARGET: {target_sum_gmv if target_sum_gmv else f'Cân bằng với các khung khác (±{max_gmv_diff*100:.0f}%)'}
4. 🎯 CATEGORY MATCH: Chỉ chọn từ categories: {', '.join(timeslot['allowed_categories'])}

CRITICAL CONSTRAINTS:
- ABSOLUTELY NO BRAND DUPLICATION across all timeslots
- ZERO TOLERANCE on quota deviation
- SUM GMV must follow high/normal traffic rules
- Each brand name must be unique across entire allocation

RETURN JSON:
{{
  "{timeslot['name']}": [
    {{"name": "Brand", "category": "Cat", "type": "Type", "sku_review": X, "gmv": Y}},
    ...
  ]
}}

FINAL VALIDATION REQUIRED:
- NO brand appears in "BRANDS ALREADY USED" list
- Total SKU reviews = EXACTLY as specified per category
- SUM GMV follows traffic type rules
- All brands from allowed categories only"""

        # Thêm kiểm tra cuối cùng dựa trên quota type
        if "category_quotas" in timeslot and timeslot["category_quotas"]:
            prompt += "\n- Quota theo category: CHÍNH XÁC như yêu cầu"
            for category, quota in timeslot["category_quotas"].items():
                if quota > 0:
                    prompt += f"\n  • {category}: đúng {quota} SKU reviews"
        else:
            prompt += f"\n- Tổng SKU review = chính xác {timeslot['min_sku']} (±10% tối đa)"

        prompt += f"""
- Type sản phẩm phù hợp với khung giờ {timeslot_type}
- Tổng GMV cân bằng với các khung giờ khác

Ưu tiên tuân thủ quota > cân bằng GMV > tối ưu Type."""

        return prompt

    def get_smart_system_prompt(self) -> str:
        """System prompt nghiêm ngặt cho GPT với high traffic rules"""
        return """You are a STRICT brand allocation system for Shopee Vietnam livestream.

ABSOLUTE REQUIREMENTS (NO EXCEPTIONS):
1. 🚨 NO DUPLICATES: NEVER use any brand that appears in "BRANDS ALREADY USED" list - this is the most critical rule
2. 🚨 QUOTA COMPLIANCE: SKU review quotas must be EXACTLY as specified - zero tolerance for deviation
3. 💰 HIGH TRAFFIC RULES:
   - High traffic timeslots: 50-70% higher SUM GMV than normal traffic
   - Normal traffic timeslots: balanced within ±30% of each other
   - NO 10x or 1000% differences allowed
4. 🎯 CATEGORY RESTRICTION: Only use brands from allowed categories

CRITICAL CONSTRAINTS:
- Brand duplication = IMMEDIATE FAILURE - check "BRANDS ALREADY USED" list first
- If quota says "15 ELHA + 9 HB", result must be exactly 15 ELHA SKU reviews + 9 HB SKU reviews
- High traffic gets moderate bonus (50-70%), not extreme advantage
- Mathematical precision required for all calculations

OUTPUT: JSON only, no explanations, strict compliance with all rules."""

    def calculate_current_stats(self, current_results: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Tính thống kê hiện tại của các khung giờ đã sắp xếp"""
        stats = {}
        for timeslot_name, brands in current_results.items():
            if brands:
                stats[timeslot_name] = {
                    "count": len(brands),
                    "avg_gmv": self.calculate_avg_gmv(brands),
                    "sum_gmv": self.calculate_sum_gmv(brands),
                    "total_sku": self.calculate_total_sku_review(brands),
                    "categories": list(set(b["category"] for b in brands)),
                    "types": list(set(b.get("type", "") for b in brands if b.get("type", "")))
                }
        return stats

    def fallback_selection(self, suitable_brands: List[Dict[str, Any]],
                         timeslot: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """Fallback selection khi GPT fail - với logic nghiêm ngặt về quota"""
        selected_brands = []

        # Kiểm tra nếu có category quotas
        if "category_quotas" in timeslot and timeslot["category_quotas"]:
            # Sử dụng category quotas nghiêm ngặt
            category_quotas = timeslot["category_quotas"]
            category_counts = {}

            # Phân loại brands theo category
            brands_by_category = {}
            for brand in suitable_brands:
                cat = brand["category"]
                if cat not in brands_by_category:
                    brands_by_category[cat] = []
                brands_by_category[cat].append(brand)

            # Sắp xếp brands trong mỗi category theo GMV giảm dần
            for cat in brands_by_category:
                brands_by_category[cat].sort(key=lambda x: x["gmv"], reverse=True)

            # Chọn brands theo quota chính xác - LOẠI BỎ USED BRANDS
            for category, required_quota in category_quotas.items():
                if required_quota > 0 and category in brands_by_category:
                    current_quota = 0
                    for brand in brands_by_category[category]:
                        # QUAN TRỌNG: Kiểm tra brand chưa được sử dụng
                        if (brand["name"] not in self.used_brands and
                            current_quota + brand["sku_review"] <= required_quota):
                            selected_brands.append(brand)
                            current_quota += brand["sku_review"]

                            # Dừng khi đạt chính xác quota
                            if current_quota == required_quota:
                                break
        else:
            # Fallback: sử dụng min_sku tổng (không khuyến khích)
            required_sku = timeslot.get("min_sku", 0)
            total_sku = 0

            for brand in suitable_brands:
                # QUAN TRỌNG: Kiểm tra brand chưa được sử dụng
                if (brand["name"] not in self.used_brands and
                    total_sku + brand["sku_review"] <= required_sku):
                    selected_brands.append(brand)
                    total_sku += brand["sku_review"]

                    if total_sku == required_sku:
                        break

        return {timeslot["name"]: selected_brands}

    def handle_remaining_brands(self, client: openai.OpenAI,
                              remaining_brands: List[Dict[str, Any]],
                              current_results: Dict[str, List[Dict[str, Any]]],
                              max_gmv_diff: float):
        """Xử lý brands còn lại bằng cách phân bổ vào các timeslot chưa đầy"""
        if not remaining_brands:
            return

        # Tìm timeslots có thể nhận thêm brands
        available_timeslots = []
        for timeslot in self.timeslots:
            current_brands = current_results.get(timeslot["name"], [])
            if len(current_brands) < 20:  # Giới hạn tối đa 20 brands/timeslot
                available_timeslots.append(timeslot)

        if not available_timeslots:
            return

        # Phân bổ brands còn lại
        for brand in remaining_brands[:50]:  # Chỉ xử lý tối đa 50 brands còn lại
            best_timeslot = None
            best_score = -1

            for timeslot in available_timeslots:
                if brand["category"] in timeslot["allowed_categories"]:
                    score = self.calculate_brand_priority(brand, timeslot)
                    if score > best_score:
                        best_score = score
                        best_timeslot = timeslot

            if best_timeslot:
                current_results[best_timeslot["name"]].append(brand)

    def balance_final_gmv(self, results: Dict[str, List[Dict[str, Any]]],
                        max_gmv_diff: float) -> Dict[str, List[Dict[str, Any]]]:
        """Cân bằng GMV cuối cùng bằng cách swap brands giữa các timeslots - sử dụng SUM OF GMV"""
        # Tính SUM GMV hiện tại (không phải average)
        timeslot_sum_gmvs = {}
        for name, brands in results.items():
            if brands:
                timeslot_sum_gmvs[name] = self.calculate_sum_gmv(brands)

        if len(timeslot_sum_gmvs) < 2:
            return results

        avg_sum_gmv = sum(timeslot_sum_gmvs.values()) / len(timeslot_sum_gmvs)

        # Tìm timeslots cần cân bằng dựa trên SUM GMV
        high_gmv_slots = []
        low_gmv_slots = []

        for name, sum_gmv in timeslot_sum_gmvs.items():
            diff_ratio = abs(sum_gmv - avg_sum_gmv) / avg_sum_gmv
            if diff_ratio > max_gmv_diff:
                if sum_gmv > avg_sum_gmv:
                    high_gmv_slots.append(name)
                else:
                    low_gmv_slots.append(name)

        # Thực hiện swap đơn giản (có thể cải thiện thêm)
        for high_slot in high_gmv_slots:
            for low_slot in low_gmv_slots:
                if self.try_swap_brands(results, high_slot, low_slot, avg_sum_gmv):
                    break

        return results

    def try_swap_brands(self, results: Dict[str, List[Dict[str, Any]]],
                       high_slot: str, low_slot: str, target_gmv: float) -> bool:
        """Thử swap brands giữa 2 timeslots để cân bằng GMV"""
        high_brands = results[high_slot]
        low_brands = results[low_slot]

        if not high_brands or not low_brands:
            return False

        # Tìm cặp brands phù hợp để swap
        for h_brand in high_brands:
            for l_brand in low_brands:
                # Kiểm tra category compatibility
                high_timeslot = next(ts for ts in self.timeslots if ts["name"] == high_slot)
                low_timeslot = next(ts for ts in self.timeslots if ts["name"] == low_slot)

                if (h_brand["category"] in low_timeslot["allowed_categories"] and
                    l_brand["category"] in high_timeslot["allowed_categories"]):

                    # Thực hiện swap
                    high_brands.remove(h_brand)
                    high_brands.append(l_brand)
                    low_brands.remove(l_brand)
                    low_brands.append(h_brand)

                    return True

        return False

    def update_used_brands(self, results: Dict[str, List[Dict[str, Any]]]):
        """Cập nhật trạng thái brands đã được sử dụng"""
        used_brands = set()
        for brands in results.values():
            for brand in brands:
                used_brands.add(brand["name"])

        for brand in self.brands:
            brand["assigned"] = brand["name"] in used_brands

        self.used_brands = used_brands
    
    def export_results(self, file_path: str) -> None:
        """
        Xuất kết quả ra file Excel
        
        Args:
            file_path: Đường dẫn file Excel để lưu
        """
        if not self.results:
            raise ValueError("Chưa có kết quả nào để xuất. Vui lòng chạy thuật toán sắp xếp trước.")
        
        try:
            # Tạo dataframe từ kết quả bao gồm Type
            rows = []
            for timeslot_name, brands in self.results.items():
                for brand in brands:
                    row = {
                        "Khung giờ": timeslot_name,
                        "Brand": brand["name"],
                        "Danh mục": brand["category"],
                        "Type": brand.get("type", ""),
                        "SKU Review": brand["sku_review"],
                        "GMV": brand["gmv"]
                    }
                    rows.append(row)
            
            df = pd.DataFrame(rows)
            
            # Tạo writer với ExcelWriter engine
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # Sheet kết quả chi tiết
                df.to_excel(writer, sheet_name='Chi tiết', index=False)
                
                # Sheet tổng quan với SUM GMV
                summary_rows = []
                for timeslot_name, brands in self.results.items():
                    avg_gmv = self.calculate_avg_gmv(brands)
                    sum_gmv = self.calculate_sum_gmv(brands)
                    total_sku_review = self.calculate_total_sku_review(brands)

                    row = {
                        "Khung giờ": timeslot_name,
                        "Số lượng Brand": len(brands),
                        "Tổng SKU Review": total_sku_review,
                        "Average GMV": avg_gmv,
                        "SUM GMV": sum_gmv
                    }
                    summary_rows.append(row)
                
                summary_df = pd.DataFrame(summary_rows)
                summary_df.to_excel(writer, sheet_name='Tổng quan', index=False)
        
        except Exception as e:
            raise Exception(f"Lỗi khi xuất file Excel: {str(e)}")
    
    def validate_timeslot_assignment(self) -> List[str]:
        """
        Kiểm tra tính hợp lệ của việc phân bổ các brand vào khung giờ

        Returns:
            Danh sách các lỗi (nếu có)
        """
        errors = []

        if not self.results:
            return ["Chưa có kết quả sắp xếp nào"]

        # Kiểm tra mỗi brand chỉ xuất hiện một lần
        brand_counts = {}
        for timeslot_name, brands in self.results.items():
            for brand in brands:
                name = brand["name"]
                if name in brand_counts:
                    brand_counts[name] += 1
                    if brand_counts[name] > 1:
                        errors.append(f"Brand '{name}' xuất hiện {brand_counts[name]} lần")
                else:
                    brand_counts[name] = 1

        # Kiểm tra danh mục được phép cho từng khung giờ
        timeslot_dict = {ts["name"]: ts for ts in self.timeslots}
        for timeslot_name, brands in self.results.items():
            if timeslot_name in timeslot_dict:
                allowed_categories = timeslot_dict[timeslot_name]["allowed_categories"]
                for brand in brands:
                    if brand["category"] not in allowed_categories:
                        errors.append(f"Brand '{brand['name']}' không thuộc danh mục được phép trong khung giờ '{timeslot_name}'")

        # Kiểm tra quota SKU review theo từng category (nghiêm ngặt)
        for timeslot_name, brands in self.results.items():
            if timeslot_name in timeslot_dict:
                timeslot_info = timeslot_dict[timeslot_name]

                # Kiểm tra category quotas nếu có
                if "category_quotas" in timeslot_info and timeslot_info["category_quotas"]:
                    category_quotas = timeslot_info["category_quotas"]

                    # Đếm SKU review theo từng category
                    actual_counts = {}
                    for brand in brands:
                        category = brand["category"]
                        sku_count = brand["sku_review"]
                        actual_counts[category] = actual_counts.get(category, 0) + sku_count

                    # Kiểm tra từng category quota
                    for category, required_quota in category_quotas.items():
                        if required_quota > 0:
                            actual_quota = actual_counts.get(category, 0)
                            if actual_quota != required_quota:
                                if actual_quota < required_quota:
                                    errors.append(f"❌ Khung giờ '{timeslot_name}' THIẾU SKU review {category}: {actual_quota}/{required_quota} (thiếu {required_quota - actual_quota})")
                                else:
                                    errors.append(f"❌ Khung giờ '{timeslot_name}' THỪA SKU review {category}: {actual_quota}/{required_quota} (thừa {actual_quota - required_quota})")

                else:
                    # Fallback: kiểm tra tổng SKU như cũ (±10% tối đa)
                    required_sku = timeslot_info["min_sku"]
                    total_sku = sum(brand["sku_review"] for brand in brands)

                    # Cho phép sai lệch tối đa 10%
                    tolerance = max(1, int(required_sku * 0.1))  # Ít nhất 1 review
                    min_acceptable = required_sku - tolerance
                    max_acceptable = required_sku + tolerance

                    if total_sku < min_acceptable:
                        errors.append(f"❌ Khung giờ '{timeslot_name}' THIẾU SKU review: {total_sku}/{required_sku} (thiếu {required_sku - total_sku})")
                    elif total_sku > max_acceptable:
                        errors.append(f"⚠️ Khung giờ '{timeslot_name}' THỪA SKU review: {total_sku}/{required_sku} (thừa {total_sku - required_sku})")

        # Kiểm tra cân bằng SUM GMV với high traffic rules
        if len(self.results) > 1:
            high_traffic_gmvs = []
            normal_traffic_gmvs = []

            # Phân loại theo traffic type
            for timeslot_name, brands in self.results.items():
                if brands:
                    sum_gmv = self.calculate_sum_gmv(brands)
                    # Tìm timeslot info
                    ts_info = next((ts for ts in self.timeslots if ts["name"] == timeslot_name), None)
                    if ts_info:
                        if ts_info.get("high_traffic", False):
                            high_traffic_gmvs.append((timeslot_name, sum_gmv))
                        else:
                            normal_traffic_gmvs.append((timeslot_name, sum_gmv))

            # Kiểm tra normal traffic balance (±30%)
            if len(normal_traffic_gmvs) > 1:
                normal_gmvs = [gmv for _, gmv in normal_traffic_gmvs]
                avg_normal_gmv = sum(normal_gmvs) / len(normal_gmvs)
                max_allowed_diff = 0.3  # 30%

                for timeslot_name, sum_gmv in normal_traffic_gmvs:
                    diff_ratio = abs(sum_gmv - avg_normal_gmv) / avg_normal_gmv
                    if diff_ratio > max_allowed_diff:
                        errors.append(f"💰 NORMAL TRAFFIC: '{timeslot_name}' chênh lệch {diff_ratio*100:.1f}% (cho phép ±{max_allowed_diff*100:.0f}%)")
                        errors.append(f"   └─ GMV: {sum_gmv:,.0f} vs Trung bình normal: {avg_normal_gmv:,.0f}")

            # Kiểm tra high traffic rules (50-70% cao hơn normal)
            if high_traffic_gmvs and normal_traffic_gmvs:
                normal_gmvs = [gmv for _, gmv in normal_traffic_gmvs]
                avg_normal_gmv = sum(normal_gmvs) / len(normal_gmvs)

                for timeslot_name, sum_gmv in high_traffic_gmvs:
                    ratio = sum_gmv / avg_normal_gmv
                    if ratio < 1.5:  # Dưới 50%
                        errors.append(f"💰 HIGH TRAFFIC: '{timeslot_name}' quá thấp {ratio:.1f}x normal (cần 1.5-1.7x)")
                    elif ratio > 1.7:  # Trên 70%
                        errors.append(f"💰 HIGH TRAFFIC: '{timeslot_name}' quá cao {ratio:.1f}x normal (cần 1.5-1.7x)")
                    elif ratio > 10:  # Cảnh báo nghiêm trọng
                        errors.append(f"🚨 NGHIÊM TRỌNG: '{timeslot_name}' cao gấp {ratio:.1f} lần normal traffic - SAI HOÀN TOÀN!")

            # Kiểm tra duplicate brands
            all_brand_names = []
            for brands in self.results.values():
                for brand in brands:
                    all_brand_names.append(brand["name"])

            # Tìm duplicates
            seen = set()
            duplicates = set()
            for name in all_brand_names:
                if name in seen:
                    duplicates.add(name)
                else:
                    seen.add(name)

            if duplicates:
                errors.append(f"🚨 DUPLICATE BRANDS: {', '.join(list(duplicates)[:5])}{'...' if len(duplicates) > 5 else ''}")
                errors.append(f"   └─ Tổng {len(duplicates)} brands bị duplicate - VI PHẠM NGHIÊM TRỌNG!")

        return errors
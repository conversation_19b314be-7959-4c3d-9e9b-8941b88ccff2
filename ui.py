import sys
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QLabel, QPushButton, QTableWidget,
                             QTableWidgetItem, QComboBox, QFileDialog, QMessageBox,
                             QTabWidget, QSpinBox, QDoubleSpinBox, QLineEdit, QCheckBox,
                             QGroupBox, QSplitter, QHeaderView, QProgressBar, QAbstractItemView)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, pyqtSlot
from PyQt6.QtGui import QColor, QPalette

from logic import BrandSorter

class ThemeManager:
    """Quản lý theme Dark/Light Mode tự động theo hệ điều hành"""

    @staticmethod
    def is_dark_mode():
        """Kiểm tra xem hệ điều hành có đang ở Dark Mode không"""
        app = QApplication.instance()
        if app:
            palette = app.palette()
            # Kiểm tra màu nền của window - nếu tối thì là dark mode
            window_color = palette.color(QPalette.ColorRole.Window)
            return window_color.lightness() < 128
        return False

    @staticmethod
    def get_theme_colors():
        """Lấy bộ màu phù hợp với theme hiện tại"""
        is_dark = ThemeManager.is_dark_mode()

        if is_dark:
            return {
                'bg_primary': '#2b2b2b',
                'bg_secondary': '#3c3c3c',
                'bg_tertiary': '#4a4a4a',
                'text_primary': '#ffffff',
                'text_secondary': '#cccccc',
                'text_muted': '#888888',
                'accent_primary': '#0078d4',
                'accent_secondary': '#106ebe',
                'success': '#107c10',
                'warning': '#ff8c00',
                'error': '#d13438',
                'border': '#555555',
                'hover': '#404040'
            }
        else:
            return {
                'bg_primary': '#ffffff',
                'bg_secondary': '#f8f9fa',
                'bg_tertiary': '#e9ecef',
                'text_primary': '#212529',
                'text_secondary': '#495057',
                'text_muted': '#6c757d',
                'accent_primary': '#0078d4',
                'accent_secondary': '#106ebe',
                'success': '#198754',
                'warning': '#fd7e14',
                'error': '#dc3545',
                'border': '#dee2e6',
                'hover': '#f5f5f5'
            }

    @staticmethod
    def apply_theme_to_widget(widget, style_type='default'):
        """Áp dụng theme cho widget cụ thể"""
        colors = ThemeManager.get_theme_colors()

        if style_type == 'info_panel':
            widget.setStyleSheet(f"""
                QLabel {{
                    background-color: {colors['bg_secondary']};
                    color: {colors['text_primary']};
                    padding: 10px;
                    border-radius: 5px;
                    border: 1px solid {colors['border']};
                }}
            """)
        elif style_type == 'status_label':
            widget.setStyleSheet(f"""
                QLabel {{
                    color: {colors['text_muted']};
                    font-style: italic;
                }}
            """)
        elif style_type == 'success_label':
            widget.setStyleSheet(f"""
                QLabel {{
                    color: {colors['success']};
                    font-weight: bold;
                }}
            """)
        elif style_type == 'error_label':
            widget.setStyleSheet(f"""
                QLabel {{
                    color: {colors['error']};
                    font-style: italic;
                }}
            """)
        elif style_type == 'spreadsheet_id':
            widget.setStyleSheet(f"""
                QLabel {{
                    color: {colors['accent_primary']};
                    font-family: monospace;
                    font-weight: bold;
                }}
            """)


class SortingWorker(QThread):
    """Worker thread để xử lý sắp xếp brands không block UI"""
    progress_updated = pyqtSignal(int, str)  # percentage, message
    sorting_completed = pyqtSignal(dict)  # results
    sorting_failed = pyqtSignal(str)  # error message

    def __init__(self, brand_sorter, api_key, max_gmv_diff):
        super().__init__()
        self.brand_sorter = brand_sorter
        self.api_key = api_key
        self.max_gmv_diff = max_gmv_diff

    def run(self):
        """Chạy thuật toán sắp xếp trong background thread"""
        try:
            # Đặt callback để cập nhật progress
            self.brand_sorter.set_progress_callback(self.update_progress)

            # Chạy thuật toán sắp xếp
            results = self.brand_sorter.sort_brands(self.api_key, self.max_gmv_diff)

            # Emit signal khi hoàn thành
            self.sorting_completed.emit(results)

        except Exception as e:
            # Emit signal khi có lỗi
            self.sorting_failed.emit(str(e))

    def update_progress(self, percentage, message):
        """Callback để cập nhật progress"""
        self.progress_updated.emit(percentage, message)

class TimeSlotEditor(QWidget):
    """Widget để quản lý khung giờ với danh sách sẵn có và editor linh hoạt"""
    save_signal = pyqtSignal(dict)

    def __init__(self):
        super().__init__()

        self.available_categories = []  # Categories từ data
        self.current_timeslot = None

        self.layout = QVBoxLayout()
        self.setLayout(self.layout)

        # Danh sách khung giờ sẵn có
        timeslots_group = QGroupBox("📅 Danh sách khung giờ")
        timeslots_layout = QVBoxLayout()

        # Tạo danh sách khung giờ mặc định
        self.create_default_timeslots()

        # Bảng hiển thị khung giờ
        self.timeslots_table = QTableWidget()
        self.timeslots_table.setColumnCount(4)
        self.timeslots_table.setHorizontalHeaderLabels(["Khung giờ", "Danh mục", "Số SKU", "High Traffic"])
        self.timeslots_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        self.timeslots_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        self.timeslots_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)
        self.timeslots_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)
        self.timeslots_table.setColumnWidth(0, 120)
        self.timeslots_table.setColumnWidth(2, 80)
        self.timeslots_table.setColumnWidth(3, 100)

        # Chọn cả row thay vì từng cell
        self.timeslots_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        # Khóa bảng không cho edit trực tiếp (chỉ cho phép click để chọn)
        self.timeslots_table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)

        # Style đẹp cho bảng khung giờ - bỏ khung selection
        self.timeslots_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #444;
                selection-background-color: #0078d4;
                selection-color: white;
                alternate-row-color: #2a2a2a;
            }
            QTableWidget::item {
                border: none;
                padding: 8px;
                outline: none;
            }
            QTableWidget::item:selected {
                background-color: #0078d4;
                color: white;
                border: none;
                outline: none;
            }
            QTableWidget::item:focus {
                border: none;
                outline: none;
                background-color: transparent;
            }
            QTableWidget::item:hover {
                background-color: #3a3a3a;
            }
        """)

        self.timeslots_table.cellClicked.connect(self.on_timeslot_selected)
        self.timeslots_table.itemChanged.connect(self.on_timeslot_item_changed)
        timeslots_layout.addWidget(self.timeslots_table)

        # Buttons cho quản lý khung giờ
        timeslot_buttons = QHBoxLayout()
        self.add_timeslot_btn = QPushButton("➕ Thêm khung giờ")
        self.add_timeslot_btn.clicked.connect(self.add_custom_timeslot)
        timeslot_buttons.addWidget(self.add_timeslot_btn)

        self.delete_timeslot_btn = QPushButton("🗑️ Xóa khung giờ")
        self.delete_timeslot_btn.clicked.connect(self.delete_selected_timeslot)
        timeslot_buttons.addWidget(self.delete_timeslot_btn)

        self.save_timeslots_btn = QPushButton("💾 Lưu khung giờ")
        self.save_timeslots_btn.clicked.connect(self.save_all_timeslots)
        timeslot_buttons.addWidget(self.save_timeslots_btn)
        timeslots_layout.addLayout(timeslot_buttons)

        timeslots_group.setLayout(timeslots_layout)
        self.layout.addWidget(timeslots_group)

        # Editor cho khung giờ được chọn
        editor_group = QGroupBox("⚙️ Cấu hình khung giờ")
        editor_layout = QVBoxLayout()

        # Thông tin khung giờ được chọn
        self.selected_info = QLabel("Chọn một khung giờ để cấu hình")
        ThemeManager.apply_theme_to_widget(self.selected_info, 'status_label')
        editor_layout.addWidget(self.selected_info)

        # Danh mục và SKU
        categories_group = QGroupBox("📂 Danh mục và số lượng SKU")
        categories_layout = QVBoxLayout()

        self.categories_table = QTableWidget()
        self.categories_table.setColumnCount(3)
        self.categories_table.setHorizontalHeaderLabels(["Danh mục", "Bật/Tắt", "Số SKU"])
        self.categories_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        self.categories_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)
        self.categories_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        self.categories_table.setColumnWidth(1, 80)
        # Cột số SKU sẽ tự động stretch để QLineEdit chiếm trọn

        # Tăng chiều cao của các row
        self.categories_table.verticalHeader().setDefaultSectionSize(40)
        self.categories_table.itemChanged.connect(self.on_category_item_changed)

        # Style cho bảng categories với QLineEdit chiếm trọn ô
        self.categories_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #444;
                selection-background-color: #0078d4;
            }
            QTableWidget::item {
                padding: 0px;
                border: none;
            }
        """)

        categories_layout.addWidget(self.categories_table)

        categories_group.setLayout(categories_layout)
        editor_layout.addWidget(categories_group)

        # High Traffic setting - đơn giản hóa
        traffic_group = QGroupBox("🔥 Lưu lượng truy cập")
        traffic_layout = QVBoxLayout()

        self.high_traffic_cb = QCheckBox("Khung giờ có lưu lượng cao (High Traffic)")
        self.high_traffic_cb.setStyleSheet("QCheckBox { font-size: 12px; padding: 5px; }")
        traffic_layout.addWidget(self.high_traffic_cb)

        # Thêm mô tả
        traffic_desc = QLabel("💡 Khung giờ High Traffic sẽ được ưu tiên brands có GMV cao hơn")
        traffic_desc.setStyleSheet("color: gray; font-size: 10px; font-style: italic;")
        traffic_layout.addWidget(traffic_desc)

        traffic_group.setLayout(traffic_layout)
        editor_layout.addWidget(traffic_group)

        # Exclusive Timeslot setting - Tính năng mới
        exclusive_group = QGroupBox("👑 Khung giờ độc quyền")
        exclusive_layout = QVBoxLayout()

        # Checkbox bật/tắt chế độ độc quyền
        self.exclusive_cb = QCheckBox("Kích hoạt chế độ độc quyền")
        self.exclusive_cb.setStyleSheet("QCheckBox { font-size: 12px; padding: 5px; font-weight: bold; }")
        self.exclusive_cb.stateChanged.connect(self.on_exclusive_mode_changed)
        exclusive_layout.addWidget(self.exclusive_cb)

        # Container cho các controls độc quyền
        self.exclusive_controls = QWidget()
        exclusive_controls_layout = QVBoxLayout()
        self.exclusive_controls.setLayout(exclusive_controls_layout)

        # Chọn brand độc quyền
        brand_layout = QHBoxLayout()
        brand_layout.addWidget(QLabel("🏷️ Brand độc quyền:"))
        self.exclusive_brand_combo = QComboBox()
        self.exclusive_brand_combo.setPlaceholderText("Chọn brand...")
        brand_layout.addWidget(self.exclusive_brand_combo)
        exclusive_controls_layout.addLayout(brand_layout)

        # Thời lượng khung giờ độc quyền
        duration_layout = QHBoxLayout()
        duration_layout.addWidget(QLabel("⏱️ Thời lượng (phút):"))
        self.exclusive_duration_combo = QComboBox()
        self.exclusive_duration_combo.addItems(["10", "15", "20", "30", "45", "60"])
        self.exclusive_duration_combo.setCurrentText("15")
        self.exclusive_duration_combo.currentTextChanged.connect(self.on_exclusive_duration_changed)
        duration_layout.addWidget(self.exclusive_duration_combo)
        duration_layout.addStretch()
        exclusive_controls_layout.addLayout(duration_layout)

        # Mô tả logic
        self.exclusive_desc = QLabel("💡 15 phút: Thêm brands cùng danh mục với số review theo cấu hình")
        self.exclusive_desc.setStyleSheet("color: gray; font-size: 10px; font-style: italic; margin: 5px;")
        self.exclusive_desc.setWordWrap(True)
        exclusive_controls_layout.addWidget(self.exclusive_desc)

        # Ẩn controls ban đầu
        self.exclusive_controls.setVisible(False)
        exclusive_layout.addWidget(self.exclusive_controls)

        exclusive_group.setLayout(exclusive_layout)
        editor_layout.addWidget(exclusive_group)

        # Apply button
        apply_layout = QHBoxLayout()
        self.apply_btn = QPushButton("✅ Áp dụng cấu hình")
        self.apply_btn.clicked.connect(self.apply_configuration)
        self.apply_btn.setEnabled(False)
        apply_layout.addWidget(self.apply_btn)
        apply_layout.addStretch()
        editor_layout.addLayout(apply_layout)

        editor_group.setLayout(editor_layout)
        self.layout.addWidget(editor_group)

        # Load danh sách khung giờ mặc định
        self.load_default_timeslots()

    def create_default_timeslots(self):
        """Tạo danh sách khung giờ mặc định (12:00-24:00)"""
        self.default_timeslots = [
            {"name": "12:00-13:00", "start_time": "12:00", "end_time": "13:00", "categories": {}, "high_traffic": True, "exclusive": False, "exclusive_brand": None, "exclusive_duration": 60},
            {"name": "13:00-14:00", "start_time": "13:00", "end_time": "14:00", "categories": {}, "high_traffic": True, "exclusive": False, "exclusive_brand": None, "exclusive_duration": 60},
            {"name": "14:00-15:00", "start_time": "14:00", "end_time": "15:00", "categories": {}, "high_traffic": False, "exclusive": False, "exclusive_brand": None, "exclusive_duration": 60},
            {"name": "15:00-16:00", "start_time": "15:00", "end_time": "16:00", "categories": {}, "high_traffic": False, "exclusive": False, "exclusive_brand": None, "exclusive_duration": 60},
            {"name": "16:00-17:00", "start_time": "16:00", "end_time": "17:00", "categories": {}, "high_traffic": False, "exclusive": False, "exclusive_brand": None, "exclusive_duration": 60},
            {"name": "17:00-18:00", "start_time": "17:00", "end_time": "18:00", "categories": {}, "high_traffic": False, "exclusive": False, "exclusive_brand": None, "exclusive_duration": 60},
            {"name": "18:00-19:00", "start_time": "18:00", "end_time": "19:00", "categories": {}, "high_traffic": False, "exclusive": False, "exclusive_brand": None, "exclusive_duration": 60},
            {"name": "19:00-20:00", "start_time": "19:00", "end_time": "20:00", "categories": {}, "high_traffic": True, "exclusive": False, "exclusive_brand": None, "exclusive_duration": 60},
            {"name": "20:00-21:00", "start_time": "20:00", "end_time": "21:00", "categories": {}, "high_traffic": True, "exclusive": False, "exclusive_brand": None, "exclusive_duration": 60},
            {"name": "21:00-22:00", "start_time": "21:00", "end_time": "22:00", "categories": {}, "high_traffic": False, "exclusive": False, "exclusive_brand": None, "exclusive_duration": 60},
            {"name": "22:00-23:00", "start_time": "22:00", "end_time": "23:00", "categories": {}, "high_traffic": False, "exclusive": False, "exclusive_brand": None, "exclusive_duration": 60},
            {"name": "23:00-24:00", "start_time": "23:00", "end_time": "24:00", "categories": {}, "high_traffic": False, "exclusive": False, "exclusive_brand": None, "exclusive_duration": 60}
        ]

    def load_default_timeslots(self):
        """Load danh sách khung giờ vào bảng"""
        # Cập nhật số cột để bao gồm cột Độc quyền
        self.timeslots_table.setColumnCount(5)
        self.timeslots_table.setHorizontalHeaderLabels(["Khung giờ", "Danh mục", "Số SKU", "High Traffic", "Độc quyền"])

        # Cập nhật width cho cột mới
        self.timeslots_table.setColumnWidth(4, 100)

        self.timeslots_table.setRowCount(len(self.default_timeslots))

        for i, timeslot in enumerate(self.default_timeslots):
            # Tên khung giờ
            self.timeslots_table.setItem(i, 0, QTableWidgetItem(timeslot["name"]))

            # Danh mục (hiển thị tóm tắt hoặc thông tin độc quyền)
            if timeslot.get("exclusive", False):
                exclusive_info = f"👑 {timeslot.get('exclusive_brand', 'Chưa chọn')} ({timeslot.get('exclusive_duration', 60)}p)"
                self.timeslots_table.setItem(i, 1, QTableWidgetItem(exclusive_info))
            else:
                categories_summary = self.get_categories_summary(timeslot["categories"])
                self.timeslots_table.setItem(i, 1, QTableWidgetItem(categories_summary))

            # Tổng số SKU
            total_sku = sum(timeslot["categories"].values())
            self.timeslots_table.setItem(i, 2, QTableWidgetItem(str(total_sku)))

            # High Traffic checkbox
            traffic_item = QTableWidgetItem()
            traffic_item.setFlags(traffic_item.flags() | Qt.ItemFlag.ItemIsUserCheckable)
            traffic_item.setCheckState(Qt.CheckState.Checked if timeslot["high_traffic"] else Qt.CheckState.Unchecked)
            traffic_item.setText("High Traffic")
            self.timeslots_table.setItem(i, 3, traffic_item)

            # Exclusive checkbox
            exclusive_item = QTableWidgetItem()
            exclusive_item.setFlags(exclusive_item.flags() | Qt.ItemFlag.ItemIsUserCheckable)
            exclusive_item.setCheckState(Qt.CheckState.Checked if timeslot.get("exclusive", False) else Qt.CheckState.Unchecked)
            exclusive_item.setText("Độc quyền")
            self.timeslots_table.setItem(i, 4, exclusive_item)

    def get_categories_summary(self, categories):
        """Tạo tóm tắt danh mục cho hiển thị"""
        if not categories:
            return "Chưa cấu hình"

        active_categories = [cat for cat, sku in categories.items() if sku > 0]
        if not active_categories:
            return "Chưa cấu hình"

        if len(active_categories) <= 2:
            return ", ".join(active_categories)
        else:
            return f"{', '.join(active_categories[:2])}... (+{len(active_categories)-2})"

    def update_categories(self, categories):
        """Cập nhật danh sách categories từ data"""
        self.available_categories = categories
        self.refresh_categories_table()

    def update_brands(self, brands):
        """Cập nhật danh sách brands cho exclusive brand selection"""
        self.available_brands = brands
        self.refresh_exclusive_brand_combo()

    def refresh_categories_table(self):
        """Refresh bảng categories trong editor - RESET theo timeslot hiện tại"""
        if not self.current_timeslot:
            return

        # Tạm thời disconnect để tránh trigger events khi refresh
        try:
            self.categories_table.itemChanged.disconnect()
        except:
            pass

        self.categories_table.setRowCount(len(self.available_categories))

        for i, category in enumerate(self.available_categories):
            # Tên danh mục
            self.categories_table.setItem(i, 0, QTableWidgetItem(category))

            # Checkbox bật/tắt - RESET theo timeslot hiện tại
            checkbox = QTableWidgetItem()
            checkbox.setFlags(Qt.ItemFlag.ItemIsUserCheckable | Qt.ItemFlag.ItemIsEnabled)

            # Chỉ check nếu category có trong timeslot hiện tại VÀ có SKU > 0
            is_enabled = (category in self.current_timeslot["categories"] and
                         self.current_timeslot["categories"][category] > 0)
            checkbox.setCheckState(Qt.CheckState.Checked if is_enabled else Qt.CheckState.Unchecked)
            self.categories_table.setItem(i, 1, checkbox)

            # Số SKU - QLineEdit chiếm trọn ô
            sku_value = self.current_timeslot["categories"].get(category, 0)
            sku_input = QLineEdit()
            sku_input.setText(str(sku_value))
            sku_input.setPlaceholderText("0")
            sku_input.setAlignment(Qt.AlignmentFlag.AlignCenter)
            sku_input.textChanged.connect(self.on_sku_input_changed)

            # Style để chiếm trọn ô
            sku_input.setStyleSheet("""
                QLineEdit {
                    border: none;
                    background-color: transparent;
                    padding: 8px;
                    font-size: 14px;
                }
                QLineEdit:focus {
                    background-color: #2b2b2b;
                    border: 1px solid #0078d4;
                }
            """)

            self.categories_table.setCellWidget(i, 2, sku_input)

        # Reconnect sau khi refresh xong
        self.categories_table.itemChanged.connect(self.on_category_item_changed)

    def on_timeslot_selected(self, row, column):
        """Xử lý khi chọn một khung giờ"""
        _ = column  # Unused parameter
        if row < len(self.default_timeslots):
            # Lưu cấu hình của timeslot cũ trước khi chuyển
            if self.current_timeslot:
                self.save_current_timeslot_config()

            # Chuyển sang timeslot mới
            self.current_timeslot = self.default_timeslots[row]
            self.selected_info.setText(f"📅 Đang cấu hình: {self.current_timeslot['name']}")
            ThemeManager.apply_theme_to_widget(self.selected_info, 'success_label')

            # Cập nhật high traffic checkbox theo timeslot mới
            self.high_traffic_cb.setChecked(self.current_timeslot["high_traffic"])

            # Cập nhật exclusive settings theo timeslot mới
            self.exclusive_cb.setChecked(self.current_timeslot.get("exclusive", False))
            self.on_exclusive_mode_changed()  # Cập nhật visibility của controls

            if self.current_timeslot.get("exclusive", False):
                # Set exclusive brand nếu có
                exclusive_brand = self.current_timeslot.get("exclusive_brand")
                if exclusive_brand:
                    index = self.exclusive_brand_combo.findText(exclusive_brand)
                    if index >= 0:
                        self.exclusive_brand_combo.setCurrentIndex(index)

                # Set exclusive duration
                duration = str(self.current_timeslot.get("exclusive_duration", 60))
                index = self.exclusive_duration_combo.findText(duration)
                if index >= 0:
                    self.exclusive_duration_combo.setCurrentIndex(index)
                self.on_exclusive_duration_changed()  # Cập nhật description

            # Refresh categories table với dữ liệu của timeslot mới
            self.refresh_categories_table()

            # Enable apply button
            self.apply_btn.setEnabled(True)

    def save_current_timeslot_config(self):
        """Lưu cấu hình của timeslot hiện tại trước khi chuyển"""
        if not self.current_timeslot:
            return

        # Cập nhật high traffic
        self.current_timeslot["high_traffic"] = self.high_traffic_cb.isChecked()

        # Cập nhật exclusive settings
        self.current_timeslot["exclusive"] = self.exclusive_cb.isChecked()
        if self.exclusive_cb.isChecked():
            self.current_timeslot["exclusive_brand"] = self.exclusive_brand_combo.currentText()
            self.current_timeslot["exclusive_duration"] = int(self.exclusive_duration_combo.currentText())
        else:
            self.current_timeslot["exclusive_brand"] = None
            self.current_timeslot["exclusive_duration"] = 60

        # Cập nhật categories từ bảng (chỉ khi không phải exclusive mode)
        if not self.exclusive_cb.isChecked():
            self.current_timeslot["categories"] = {}
            for i in range(self.categories_table.rowCount()):
                # Kiểm tra các item có tồn tại không
                category_item = self.categories_table.item(i, 0)
                checkbox_item = self.categories_table.item(i, 1)
                sku_widget = self.categories_table.cellWidget(i, 2)  # QLineEdit widget

                if not category_item or not checkbox_item or not sku_widget:
                    continue

                category = category_item.text()

                # Lấy giá trị từ QLineEdit và validate
                try:
                    sku_value = int(sku_widget.text()) if sku_widget.text().strip() else 0
                except (ValueError, AttributeError):
                    sku_value = 0

                if checkbox_item.checkState() == Qt.CheckState.Checked and sku_value > 0:
                    self.current_timeslot["categories"][category] = sku_value

    def on_timeslot_item_changed(self, item):
        """Xử lý khi item trong bảng timeslots thay đổi"""
        row = item.row()
        if row >= len(self.default_timeslots):
            return

        if item.column() == 3:  # Cột High Traffic
            # Cập nhật trạng thái high_traffic
            self.default_timeslots[row]["high_traffic"] = (item.checkState() == Qt.CheckState.Checked)
            # Nếu đang edit timeslot này, cập nhật checkbox
            if self.current_timeslot and self.current_timeslot == self.default_timeslots[row]:
                self.high_traffic_cb.setChecked(self.default_timeslots[row]["high_traffic"])

        elif item.column() == 4:  # Cột Độc quyền
            # Cập nhật trạng thái exclusive
            self.default_timeslots[row]["exclusive"] = (item.checkState() == Qt.CheckState.Checked)
            # Nếu đang edit timeslot này, cập nhật checkbox
            if self.current_timeslot and self.current_timeslot == self.default_timeslots[row]:
                self.exclusive_cb.setChecked(self.default_timeslots[row]["exclusive"])
                self.on_exclusive_mode_changed()  # Cập nhật visibility

    def on_category_item_changed(self, item):
        """Xử lý khi item trong bảng categories thay đổi (chỉ checkbox)"""
        if not self.current_timeslot or item.column() != 1:  # Chỉ xử lý cột checkbox
            return

        # Cập nhật categories của timeslot hiện tại
        self.update_current_timeslot_categories()

    def on_sku_input_changed(self):
        """Xử lý khi SKU input thay đổi"""
        if not self.current_timeslot:
            return
        # Cập nhật categories của timeslot hiện tại
        self.update_current_timeslot_categories()

    def on_sku_changed(self):
        """Xử lý khi số SKU thay đổi (legacy function - giữ lại cho tương thích)"""
        if not self.current_timeslot:
            return
        self.update_current_timeslot_categories()

    def update_current_timeslot_categories(self):
        """Cập nhật categories của timeslot hiện tại"""
        if not self.current_timeslot:
            return

        # Cập nhật categories từ bảng
        for i in range(self.categories_table.rowCount()):
            # Kiểm tra các item có tồn tại không
            category_item = self.categories_table.item(i, 0)
            checkbox_item = self.categories_table.item(i, 1)
            sku_widget = self.categories_table.cellWidget(i, 2)  # QLineEdit widget

            if not category_item or not checkbox_item or not sku_widget:
                continue

            category = category_item.text()

            # Lấy giá trị từ QLineEdit và validate
            try:
                sku_value = int(sku_widget.text()) if sku_widget.text().strip() else 0
            except (ValueError, AttributeError):
                sku_value = 0

            if checkbox_item.checkState() == Qt.CheckState.Checked and sku_value > 0:
                self.current_timeslot["categories"][category] = sku_value
            else:
                self.current_timeslot["categories"].pop(category, None)

    def apply_configuration(self):
        """Áp dụng cấu hình cho khung giờ hiện tại"""
        if not self.current_timeslot:
            return

        # Sử dụng hàm save_current_timeslot_config để lưu
        self.save_current_timeslot_config()

        # Refresh hiển thị trong bảng chính
        self.load_default_timeslots()

        QMessageBox.information(self, "Thành công", f"Đã cấu hình khung giờ {self.current_timeslot['name']}")

    def add_custom_timeslot(self):
        """Thêm khung giờ tùy chỉnh"""
        from PyQt6.QtWidgets import QInputDialog

        time_range, ok = QInputDialog.getText(self, "Thêm khung giờ", "Nhập khung giờ (VD: 09:00-10:00):")
        if ok and time_range:
            try:
                start_time, end_time = time_range.split('-')
                new_timeslot = {
                    "name": time_range,
                    "start_time": start_time.strip(),
                    "end_time": end_time.strip(),
                    "categories": {},
                    "high_traffic": False,
                    "exclusive": False,
                    "exclusive_brand": None,
                    "exclusive_duration": 60
                }
                self.default_timeslots.append(new_timeslot)
                self.load_default_timeslots()
                QMessageBox.information(self, "Thành công", f"Đã thêm khung giờ {time_range}")
            except:
                QMessageBox.warning(self, "Lỗi", "Format không đúng. Vui lòng nhập theo format HH:MM-HH:MM")

    def delete_selected_timeslot(self):
        """Xóa khung giờ được chọn"""
        current_row = self.timeslots_table.currentRow()
        if current_row >= 0 and current_row < len(self.default_timeslots):
            timeslot_name = self.default_timeslots[current_row]["name"]
            reply = QMessageBox.question(self, "Xác nhận", f"Bạn có chắc muốn xóa khung giờ {timeslot_name}?")
            if reply == QMessageBox.StandardButton.Yes:
                del self.default_timeslots[current_row]
                self.load_default_timeslots()
                self.current_timeslot = None
                self.selected_info.setText("Chọn một khung giờ để cấu hình")
                ThemeManager.apply_theme_to_widget(self.selected_info, 'status_label')
                self.apply_btn.setEnabled(False)

    def save_all_timeslots(self):
        """Lưu tất cả khung giờ"""
        # Convert sang format cũ để tương thích
        converted_timeslots = []
        for ts in self.default_timeslots:
            # Lưu khung giờ nếu đã cấu hình categories HOẶC là exclusive
            if ts["categories"] or ts.get("exclusive", False):
                converted_ts = {
                    "name": ts["name"],
                    "start_time": ts["start_time"],
                    "end_time": ts["end_time"],
                    "allowed_categories": list(ts["categories"].keys()),
                    "high_traffic": ts["high_traffic"],
                    "min_sku": sum(ts["categories"].values()),  # Tổng SKU
                    "exclusive": ts.get("exclusive", False),
                    "exclusive_brand": ts.get("exclusive_brand"),
                    "exclusive_duration": ts.get("exclusive_duration", 60)
                }
                converted_timeslots.append(converted_ts)

        if converted_timeslots:
            self.save_signal.emit({"timeslots": converted_timeslots})
            QMessageBox.information(self, "Thành công", f"Đã lưu {len(converted_timeslots)} khung giờ")
        else:
            QMessageBox.warning(self, "Cảnh báo", "Chưa có khung giờ nào được cấu hình")

    def on_exclusive_mode_changed(self):
        """Xử lý khi chế độ độc quyền thay đổi"""
        is_exclusive = self.exclusive_cb.isChecked()
        self.exclusive_controls.setVisible(is_exclusive)

        # Cập nhật mô tả dựa trên duration hiện tại
        if is_exclusive:
            self.on_exclusive_duration_changed()

        # Disable/enable categories table
        self.categories_table.setEnabled(not is_exclusive)

        # Cập nhật current timeslot nếu có
        if self.current_timeslot:
            self.current_timeslot["exclusive"] = is_exclusive
            if not is_exclusive:
                self.current_timeslot["exclusive_brand"] = None
                self.current_timeslot["exclusive_duration"] = 60

    def on_exclusive_duration_changed(self):
        """Xử lý khi thời lượng độc quyền thay đổi"""
        duration = int(self.exclusive_duration_combo.currentText())

        if duration == 60:
            desc = "💡 60 phút: Có thể thêm brands cùng danh mục với review = 0 để cân bằng GMV"
        else:
            desc = f"💡 {duration} phút: Thêm brands cùng danh mục với số review theo cấu hình"

        self.exclusive_desc.setText(desc)

        # Cập nhật current timeslot nếu có
        if self.current_timeslot:
            self.current_timeslot["exclusive_duration"] = duration

    def refresh_exclusive_brand_combo(self):
        """Refresh danh sách brands cho exclusive selection"""
        if not hasattr(self, 'available_brands'):
            return

        current_text = self.exclusive_brand_combo.currentText()
        self.exclusive_brand_combo.clear()

        # Thêm placeholder
        self.exclusive_brand_combo.addItem("Chọn brand...")

        # Thêm các brands
        brand_names = [brand.get('name', brand.get('brand', '')) for brand in self.available_brands]
        brand_names = [name for name in brand_names if name]  # Lọc bỏ empty
        brand_names.sort()  # Sắp xếp alphabetically

        self.exclusive_brand_combo.addItems(brand_names)

        # Restore selection nếu có
        if current_text:
            index = self.exclusive_brand_combo.findText(current_text)
            if index >= 0:
                self.exclusive_brand_combo.setCurrentIndex(index)

class BrandManagerApp(QMainWindow):
    def __init__(self):
        super().__init__()

        self.setWindowTitle("Quản lý sắp xếp Brand Livestream")
        self.setGeometry(100, 100, 1200, 800)

        self.brand_sorter = BrandSorter()

        # Áp dụng theme cho toàn bộ ứng dụng
        self.apply_global_theme()

        self.init_ui()

    def apply_global_theme(self):
        """Áp dụng theme toàn cục cho ứng dụng"""
        colors = ThemeManager.get_theme_colors()

        # Style cho toàn bộ ứng dụng
        global_style = f"""
            QMainWindow {{
                background-color: {colors['bg_primary']};
                color: {colors['text_primary']};
            }}

            QWidget {{
                background-color: {colors['bg_primary']};
                color: {colors['text_primary']};
            }}

            QTabWidget::pane {{
                border: 1px solid {colors['border']};
                background-color: {colors['bg_primary']};
            }}

            QTabBar::tab {{
                background-color: {colors['bg_secondary']};
                color: {colors['text_primary']};
                padding: 8px 16px;
                margin-right: 2px;
                border: 1px solid {colors['border']};
                border-bottom: none;
            }}

            QTabBar::tab:selected {{
                background-color: {colors['bg_primary']};
                border-bottom: 2px solid {colors['accent_primary']};
            }}

            QTabBar::tab:hover {{
                background-color: {colors['hover']};
            }}

            QGroupBox {{
                font-weight: bold;
                border: 2px solid {colors['border']};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: {colors['bg_primary']};
                color: {colors['text_primary']};
            }}

            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: {colors['accent_primary']};
            }}

            QPushButton {{
                background-color: {colors['accent_primary']};
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }}

            QPushButton:hover {{
                background-color: {colors['accent_secondary']};
            }}

            QPushButton:pressed {{
                background-color: {colors['accent_secondary']};
            }}

            QLineEdit, QComboBox {{
                background-color: {colors['bg_secondary']};
                color: {colors['text_primary']};
                border: 1px solid {colors['border']};
                padding: 6px;
                border-radius: 4px;
            }}

            QLineEdit:focus, QComboBox:focus {{
                border: 2px solid {colors['accent_primary']};
            }}

            QTableWidget {{
                background-color: {colors['bg_primary']};
                color: {colors['text_primary']};
                gridline-color: {colors['border']};
                selection-background-color: {colors['accent_primary']};
            }}

            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {colors['border']};
            }}

            QTableWidget::item:selected {{
                background-color: {colors['accent_primary']};
                color: white;
            }}

            QHeaderView::section {{
                background-color: {colors['bg_secondary']};
                color: {colors['text_primary']};
                padding: 8px;
                border: 1px solid {colors['border']};
                font-weight: bold;
            }}

            QProgressBar {{
                border: 1px solid {colors['border']};
                border-radius: 4px;
                text-align: center;
                background-color: {colors['bg_secondary']};
            }}

            QProgressBar::chunk {{
                background-color: {colors['accent_primary']};
                border-radius: 3px;
            }}

            QTextEdit {{
                background-color: {colors['bg_secondary']};
                color: {colors['text_primary']};
                border: 1px solid {colors['border']};
                border-radius: 4px;
                padding: 8px;
            }}

            QSpinBox, QDoubleSpinBox {{
                background-color: {colors['bg_secondary']};
                color: {colors['text_primary']};
                border: 1px solid {colors['border']};
                padding: 6px;
                border-radius: 4px;
            }}

            QCheckBox {{
                color: {colors['text_primary']};
            }}

            QCheckBox::indicator {{
                width: 16px;
                height: 16px;
                border: 1px solid {colors['border']};
                border-radius: 3px;
                background-color: {colors['bg_secondary']};
            }}

            QCheckBox::indicator:checked {{
                background-color: {colors['accent_primary']};
                border-color: {colors['accent_primary']};
            }}
        """

        self.setStyleSheet(global_style)

    def refresh_theme(self):
        """Làm mới theme khi có thay đổi từ hệ điều hành"""
        self.apply_global_theme()

        # Cập nhật lại các widget đặc biệt (spreadsheet_id_label đã bị xóa)

        if hasattr(self, 'gsheets_status_label'):
            ThemeManager.apply_theme_to_widget(self.gsheets_status_label, 'status_label')

        # Force repaint
        self.update()

    def init_ui(self):
        central_widget = QWidget()
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)
        
        # Tạo tabs chính
        self.tabs = QTabWidget()
        
        # Tab 1: Quản lý dữ liệu
        data_tab = QWidget()
        data_layout = QVBoxLayout()
        data_tab.setLayout(data_layout)
        
        # Tạo layout cho phần nhập dữ liệu Google Sheets
        input_group = QGroupBox("📊 Nhập dữ liệu từ Google Sheets")
        input_layout = QVBoxLayout()

        # URL/Spreadsheet ID input với auto-parse
        url_layout = QHBoxLayout()
        url_layout.addWidget(QLabel("🔗 Spreadsheet URL/ID:"))
        self.gsheets_url_input = QLineEdit()
        self.gsheets_url_input.setPlaceholderText("Paste URL hoặc nhập Spreadsheet ID...")
        self.gsheets_url_input.textChanged.connect(self.on_url_changed)
        url_layout.addWidget(self.gsheets_url_input)
        input_layout.addLayout(url_layout)

        # Sheet selection với combo box
        sheet_layout = QHBoxLayout()
        sheet_layout.addWidget(QLabel("📋 Chọn Sheet:"))
        self.sheet_combo = QComboBox()
        self.sheet_combo.setEditable(True)
        self.sheet_combo.setPlaceholderText("brandlist (mặc định)")
        sheet_layout.addWidget(self.sheet_combo)

        self.load_sheet_btn = QPushButton("📋 Load sheet")
        self.load_sheet_btn.clicked.connect(self.load_sheet_info)
        sheet_layout.addWidget(self.load_sheet_btn)

        self.refresh_sheets_btn = QPushButton("🔄 Làm mới")
        self.refresh_sheets_btn.clicked.connect(self.refresh_sheet_list)
        sheet_layout.addWidget(self.refresh_sheets_btn)
        input_layout.addLayout(sheet_layout)

        # Row range input với nút nhập dữ liệu cùng dòng
        range_layout = QHBoxLayout()
        range_layout.addWidget(QLabel("� Dòng dữ liệu:"))
        range_layout.addWidget(QLabel("Từ dòng 2 đến dòng:"))
        self.end_row_input = QLineEdit()
        self.end_row_input.setPlaceholderText("Để trống = tất cả")
        self.end_row_input.setMaximumWidth(100)
        range_layout.addWidget(self.end_row_input)

        # Thêm nút nhập dữ liệu vào cùng dòng
        self.import_gsheets_btn = QPushButton("📊 Nhập dữ liệu")
        self.import_gsheets_btn.clicked.connect(self.import_google_sheets)
        range_layout.addWidget(self.import_gsheets_btn)

        range_layout.addStretch()
        input_layout.addLayout(range_layout)

        # Status label
        self.gsheets_status_label = QLabel("Sẵn sàng nhập từ Google Sheets")
        ThemeManager.apply_theme_to_widget(self.gsheets_status_label, 'status_label')
        input_layout.addWidget(self.gsheets_status_label)

        input_group.setLayout(input_layout)
        data_layout.addWidget(input_group)
        
        # Bảng hiển thị dữ liệu brand
        self.brand_table = QTableWidget()
        self.brand_table.setColumnCount(5)
        self.brand_table.setHorizontalHeaderLabels(["Brand", "Danh mục", "SKU Review", "GMV", "Đã sắp xếp"])
        self.brand_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        data_layout.addWidget(self.brand_table)
        
        # Tab 2: Quản lý khung giờ
        timeslot_tab = QWidget()
        timeslot_layout = QVBoxLayout()
        timeslot_tab.setLayout(timeslot_layout)

        # Sử dụng TimeSlotEditor mới
        self.timeslot_editor = TimeSlotEditor()
        self.timeslot_editor.save_signal.connect(self.save_timeslots)
        timeslot_layout.addWidget(self.timeslot_editor)
        
        # Tab 3: Kết quả sắp xếp
        result_tab = QWidget()
        result_layout = QVBoxLayout()
        result_tab.setLayout(result_layout)
        
        # Các cài đặt cho thuật toán sắp xếp
        settings_group = QGroupBox("🇻🇳 Thuật toán AI cho Shopee Vietnam")
        settings_layout = QVBoxLayout()

        # Thông tin thuật toán
        algo_info = QLabel("🛒 AI chuyên biệt cho Shopee VN: Phân tích hành vi người Việt, khí hậu nhiệt đới, văn hóa mua sắm")
        algo_info.setWordWrap(True)
        colors = ThemeManager.get_theme_colors()
        algo_info.setStyleSheet(f"color: {colors['accent_primary']}; font-weight: bold; margin: 5px;")
        settings_layout.addWidget(algo_info)

        # Ngày livestream setting
        date_layout = QHBoxLayout()
        date_layout.addWidget(QLabel("📅 Ngày livestream:"))
        self.livestream_date_input = QLineEdit()
        self.livestream_date_input.setPlaceholderText("DD/MM/YYYY (để trống = hôm nay)")
        self.livestream_date_input.textChanged.connect(self.on_livestream_date_changed)
        date_layout.addWidget(self.livestream_date_input)

        self.date_info_label = QLabel("🤖 Auto: Hôm nay")
        ThemeManager.apply_theme_to_widget(self.date_info_label, 'status_label')
        date_layout.addWidget(self.date_info_label)
        settings_layout.addLayout(date_layout)

        gmv_diff_layout = QHBoxLayout()
        gmv_diff_layout.addWidget(QLabel("Chênh lệch GMV tối đa (%):"))
        self.gmv_diff_input = QDoubleSpinBox()
        self.gmv_diff_input.setRange(0, 100)
        self.gmv_diff_input.setValue(30)
        gmv_diff_layout.addWidget(self.gmv_diff_input)
        settings_layout.addLayout(gmv_diff_layout)

        # Hiển thị context Shopee Vietnam
        context_layout = QVBoxLayout()
        context_layout.addWidget(QLabel("🇻🇳 Shopee Vietnam Context:"))

        import datetime
        current_month = datetime.datetime.now().month
        season_map = {
            12: "❄️ Mùa đông VN (khô, mát)", 1: "❄️ Mùa đông VN (khô, mát)", 2: "❄️ Mùa đông VN (khô, mát)",
            3: "🌸 Mùa xuân VN (ẩm, nóng dần)", 4: "🌸 Mùa xuân VN (ẩm, nóng dần)", 5: "🌸 Mùa xuân VN (ẩm, nóng dần)",
            6: "☀️ Mùa hè VN (nóng, ẩm)", 7: "☀️ Mùa hè VN (nóng, ẩm)", 8: "☀️ Mùa hè VN (nóng, ẩm)",
            9: "🍂 Mùa thu VN (mát, khô dần)", 10: "🍂 Mùa thu VN (mát, khô dần)", 11: "🍂 Mùa thu VN (mát, khô dần)"
        }

        season_label = QLabel(f"• {season_map.get(current_month, '🍂 Mùa thu VN')} - Tháng {current_month}")
        context_layout.addWidget(season_label)

        # Shopee Vietnam events
        if current_month in [11, 12]:
            context_layout.addWidget(QLabel("• 🛍️ Black Friday + 12.12 Shopee Sale (thời trang, điện tử hot)"))
        elif current_month == 1:
            context_layout.addWidget(QLabel("• 🧧 Tết Nguyên Đán (đồ ăn, quà tặng, thời trang)"))
        elif current_month in [6, 7, 8]:
            context_layout.addWidget(QLabel("• 🏖️ Shopee Summer Sale (làm đẹp chống nắng, đồ mát)"))
        elif current_month == 9:
            context_layout.addWidget(QLabel("• 🥮 Trung Thu + Back to school (đồ ăn, học tập)"))
        elif current_month in [3, 10]:
            context_layout.addWidget(QLabel("• 💐 Ngày Phụ nữ VN (làm đẹp, thời trang, quà tặng)"))

        # Vietnam shopping behavior
        context_layout.addWidget(QLabel("• 📱 Peak hours: 11-14h (lunch break), 18-22h (after work)"))
        context_layout.addWidget(QLabel("• 💰 Người Việt: Price-sensitive, review-focused, mobile-first"))

        settings_layout.addLayout(context_layout)

        settings_group.setLayout(settings_layout)
        result_layout.addWidget(settings_group)
        
        # Button để chạy thuật toán sắp xếp
        run_btn_layout = QHBoxLayout()
        self.run_algorithm_btn = QPushButton("Chạy thuật toán sắp xếp")
        self.run_algorithm_btn.clicked.connect(self.run_sorting_algorithm)
        run_btn_layout.addWidget(self.run_algorithm_btn)
        result_layout.addLayout(run_btn_layout)

        # Progress bar và status
        progress_group = QGroupBox("Tiến trình xử lý")
        progress_layout = QVBoxLayout()

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)

        self.status_label = QLabel("Sẵn sàng")
        progress_layout.addWidget(self.status_label)

        progress_group.setLayout(progress_layout)
        result_layout.addWidget(progress_group)
        
        # Bảng kết quả
        self.result_tabs = QTabWidget()
        result_layout.addWidget(self.result_tabs)
        
        # Button xuất kết quả
        export_btn_layout = QHBoxLayout()
        self.export_result_btn = QPushButton("Xuất kết quả ra Excel")
        self.export_result_btn.clicked.connect(self.export_results)
        export_btn_layout.addWidget(self.export_result_btn)
        result_layout.addLayout(export_btn_layout)
        
        # Thêm các tab vào tab chính
        self.tabs.addTab(data_tab, "Dữ liệu Brand")
        self.tabs.addTab(timeslot_tab, "Quản lý khung giờ")
        self.tabs.addTab(result_tab, "Kết quả sắp xếp")
        
        main_layout.addWidget(self.tabs)
    




    def on_url_changed(self):
        """Xử lý khi URL thay đổi - auto parse Spreadsheet ID và hiển thị trong input field"""
        current_text = self.gsheets_url_input.text().strip()

        # Chỉ xử lý nếu có text và là URL (có chứa http)
        if current_text and 'http' in current_text.lower():
            try:
                # Extract spreadsheet ID từ URL
                if '/spreadsheets/d/' in current_text:
                    start = current_text.find('/spreadsheets/d/') + len('/spreadsheets/d/')
                    end = current_text.find('/', start)
                    if end == -1:
                        end = current_text.find('#', start)
                    if end == -1:
                        spreadsheet_id = current_text[start:]
                    else:
                        spreadsheet_id = current_text[start:end]

                    # Kiểm tra ID hợp lệ (Google Spreadsheet ID thường dài > 20 ký tự)
                    if len(spreadsheet_id) > 20:
                        # Tạm thời disconnect signal để tránh loop
                        self.gsheets_url_input.textChanged.disconnect()

                        # Thay thế URL bằng Spreadsheet ID trong input field
                        self.gsheets_url_input.setText(spreadsheet_id)
                        self.gsheets_url_input.setPlaceholderText("Spreadsheet ID đã được parse từ URL")

                        # Reconnect signal
                        self.gsheets_url_input.textChanged.connect(self.on_url_changed)

                        # Auto refresh sheet list
                        self.refresh_sheet_list()

                        # Cập nhật status
                        self.gsheets_status_label.setText(f"✅ Đã parse Spreadsheet ID từ URL")

            except Exception as e:
                self.gsheets_status_label.setText(f"❌ Lỗi parse URL: {str(e)}")

        # Nếu text thay đổi và không phải URL, reset placeholder
        elif current_text and 'http' not in current_text.lower():
            self.gsheets_url_input.setPlaceholderText("Paste URL hoặc nhập Spreadsheet ID...")



    def refresh_sheet_list(self):
        """Làm mới danh sách sheets"""
        url = self.gsheets_url_input.text().strip()
        if not url:
            return

        try:
            self.gsheets_status_label.setText("Đang tải danh sách sheets...")

            # Lấy danh sách sheets
            sheet_names = self.brand_sorter.get_available_sheets(url)

            # Cập nhật combo box
            self.sheet_combo.clear()
            self.sheet_combo.addItems(sheet_names)

            # Tự động chọn sheet "brandlist" nếu có
            brandlist_variations = ['brandlist', 'brand list', 'Brand List', 'BRAND LIST', 'BrandList']
            for variation in brandlist_variations:
                # Tìm sheet có tên tương tự (lowercase, no spaces)
                for sheet_name in sheet_names:
                    if sheet_name.lower().replace(' ', '') == variation.lower().replace(' ', ''):
                        index = self.sheet_combo.findText(sheet_name)
                        if index >= 0:
                            self.sheet_combo.setCurrentIndex(index)
                            self.gsheets_status_label.setText(f"✅ Tìm thấy {len(sheet_names)} sheets, đã chọn '{sheet_name}'")
                            return

            # Nếu không tìm thấy brandlist, chọn sheet đầu tiên
            if sheet_names:
                self.sheet_combo.setCurrentIndex(0)
                self.gsheets_status_label.setText(f"✅ Tìm thấy {len(sheet_names)} sheets")
            else:
                self.gsheets_status_label.setText("⚠️ Không tìm thấy sheet nào")

        except Exception as e:
            self.gsheets_status_label.setText(f"❌ Lỗi tải sheets: {str(e)}")

    def load_sheet_info(self):
        """Load thông tin sheet và hiển thị preview cơ bản"""
        url = self.gsheets_url_input.text().strip()
        if not url:
            QMessageBox.warning(self, "Thiếu thông tin", "Vui lòng nhập URL Google Sheets trước")
            return

        try:
            self.gsheets_status_label.setText("📋 Đang load thông tin sheet...")

            # Tự động refresh sheet list trước
            self.refresh_sheet_list()

            # Lấy sheet name hiện tại
            sheet_name = self.sheet_combo.currentText().strip() or None

            # Preview dữ liệu để hiển thị thông tin
            preview_data = self.brand_sorter.preview_google_sheets_data(
                url, sheet_name, end_row="10"  # Chỉ load 10 dòng đầu để nhanh
            )

            if "error" in preview_data:
                QMessageBox.critical(self, "Lỗi", preview_data["error"])
                self.gsheets_status_label.setText("❌ Lỗi load sheet")
                return

            # Hiển thị thông tin sheet với mapping chi tiết
            headers = preview_data["headers"]
            column_mapping = preview_data['column_mapping']

            sheet_info = f"""📋 THÔNG TIN SHEET: {preview_data['sheet_name']}
📊 Tổng dòng dữ liệu: {preview_data['total_rows']}
📏 Range hiện tại: {preview_data.get('data_range', 'Tất cả')}

🔍 MAPPING CỘT CHI TIẾT:
"""

            mapping_icons = {
                'brand': '🏷️',
                'category': '📂',
                'type': '🔖',
                'sku_review': '⭐',
                'gmv': '💰'
            }

            # Hiển thị mapping với vị trí cột
            for field, column in column_mapping.items():
                icon = mapping_icons.get(field, '📋')
                try:
                    col_index = headers.index(column) + 1  # +1 để hiển thị từ 1
                    sheet_info += f"{icon} {field.upper()}: '{column}' (cột {col_index})\n"
                except ValueError:
                    sheet_info += f"{icon} {field.upper()}: '{column}' (không tìm thấy)\n"

            # Kiểm tra cột thiếu
            required_fields = ['brand', 'category', 'sku_review', 'gmv']
            missing = [f for f in required_fields if f not in column_mapping]
            if missing:
                sheet_info += f"\n⚠️ THIẾU CỘT: {', '.join(missing.upper())}"
                sheet_info += f"\n💡 Cần có các cột: Brand, Category, SKU Review, GMV"
            else:
                sheet_info += f"\n✅ TẤT CẢ CỘT CẦN THIẾT ĐÃ CÓ"

            # Hiển thị sample data cho các cột đã map
            if preview_data.get('preview_data') and len(preview_data['preview_data']) > 0:
                sheet_info += f"\n\n📋 SAMPLE DATA (dòng đầu tiên):"
                sample_row = preview_data['preview_data'][0]
                for field, column in column_mapping.items():
                    try:
                        col_index = headers.index(column)
                        if col_index < len(sample_row):
                            value = sample_row[col_index]
                            icon = mapping_icons.get(field, '📋')
                            sheet_info += f"\n{icon} {field.upper()}: {value}"
                    except (ValueError, IndexError):
                        pass

            # Hiển thị dialog thông tin
            QMessageBox.information(self, "📋 Thông tin Sheet", sheet_info)
            self.gsheets_status_label.setText(f"✅ Đã load thông tin sheet '{preview_data['sheet_name']}'")

        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"Không thể load thông tin sheet: {str(e)}")
            self.gsheets_status_label.setText("❌ Lỗi load sheet")



    def import_google_sheets(self):
        """Import dữ liệu từ Google Sheets"""
        url = self.gsheets_url_input.text().strip()
        sheet_name = self.sheet_combo.currentText().strip() or None
        end_row = self.end_row_input.text().strip()

        if not url:
            QMessageBox.warning(self, "Thiếu thông tin", "Vui lòng nhập URL Google Sheets")
            return

        try:
            self.gsheets_status_label.setText("Đang nhập dữ liệu từ Google Sheets...")

            # Import dữ liệu với row range
            brands = self.brand_sorter.import_google_sheets(
                url, sheet_name, end_row=end_row
            )
            self.update_brand_table(brands)

            # Cập nhật danh sách danh mục cho editor khung giờ
            categories = self.brand_sorter.get_all_categories()
            self.timeslot_editor.update_categories(categories)

            # Cập nhật danh sách brands cho exclusive selection
            self.timeslot_editor.update_brands(brands)

            # Hiển thị thông tin thống kê
            total_brands = len(brands)
            total_categories = len(categories)
            total_gmv = sum(b['gmv'] for b in brands)
            avg_gmv = total_gmv / total_brands if total_brands > 0 else 0

            stats_msg = (f"🎉 NHẬP DỮ LIỆU THÀNH CÔNG!\n\n"
                       f"📊 THỐNG KÊ:\n"
                       f"• Tổng brands: {total_brands:,}\n"
                       f"• Danh mục: {total_categories}\n"
                       f"• Tổng GMV: {total_gmv:,.0f}\n"
                       f"• GMV TB: {avg_gmv:,.0f}\n\n"
                       f"🔗 Nguồn: Google Sheets\n"
                       f"📋 Sheet: {sheet_name or 'Mặc định'}\n"
                       f"📏 Dòng: 2 → {end_row or 'cuối'}")

            QMessageBox.information(self, "🎯 Hoàn thành", stats_msg)
            self.gsheets_status_label.setText(f"✅ Đã nhập {total_brands} brands")

        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"Không thể nhập dữ liệu: {str(e)}")
            self.gsheets_status_label.setText("❌ Lỗi nhập dữ liệu")

    def on_livestream_date_changed(self):
        """Xử lý khi ngày livestream thay đổi"""
        date_str = self.livestream_date_input.text().strip()

        if not date_str:
            # Nếu để trống, sử dụng ngày hiện tại
            self.brand_sorter.reset_to_current_date()
            self.date_info_label.setText("🤖 Auto: Hôm nay")
            ThemeManager.apply_theme_to_widget(self.date_info_label, 'status_label')
        else:
            try:
                # Validate format DD/MM/YYYY
                if len(date_str) == 10 and date_str[2] == '/' and date_str[5] == '/':
                    day, month, year = date_str.split('/')
                    day, month, year = int(day), int(month), int(year)

                    # Basic validation
                    if 1 <= day <= 31 and 1 <= month <= 12 and 2020 <= year <= 2030:
                        # Set ngày livestream
                        self.brand_sorter.set_livestream_date(date_str)

                        # Cập nhật thông tin hiển thị
                        import datetime
                        weekday_names = ["Thứ 2", "Thứ 3", "Thứ 4", "Thứ 5", "Thứ 6", "Thứ 7", "Chủ nhật"]
                        date_obj = datetime.datetime(year, month, day)
                        weekday = weekday_names[date_obj.weekday()]
                        season = self.brand_sorter.current_season

                        season_names = {
                            "spring": "Xuân",
                            "summer": "Hè",
                            "autumn": "Thu",
                            "winter": "Đông"
                        }
                        season_vn = season_names.get(season, season)

                        self.date_info_label.setText(f"📅 {weekday}, Mùa {season_vn}")
                        ThemeManager.apply_theme_to_widget(self.date_info_label, 'success_label')
                    else:
                        raise ValueError("Ngày không hợp lệ")
                else:
                    raise ValueError("Format không đúng")

            except Exception:
                # Hiển thị lỗi
                self.date_info_label.setText("❌ Format: DD/MM/YYYY")
                ThemeManager.apply_theme_to_widget(self.date_info_label, 'error_label')

    def update_brand_table(self, brands):
        """Cập nhật bảng brands với tối ưu cho dataset lớn"""
        # Tạm thời disable sorting để tăng tốc
        self.brand_table.setSortingEnabled(False)

        self.brand_table.setRowCount(len(brands))

        # Batch update để tăng hiệu suất
        for row, brand in enumerate(brands):
            # Tạo items một lần
            name_item = QTableWidgetItem(brand["name"])
            category_item = QTableWidgetItem(brand["category"])
            sku_item = QTableWidgetItem(str(brand["sku_review"]))
            gmv_item = QTableWidgetItem(f"{brand['gmv']:,.0f}")

            assigned_status = "Đã sắp xếp" if brand.get("assigned", False) else "Chưa sắp xếp"
            status_item = QTableWidgetItem(assigned_status)
            if brand.get("assigned", False):
                status_item.setBackground(QColor(200, 255, 200))  # Màu xanh nhạt

            # Set items
            self.brand_table.setItem(row, 0, name_item)
            self.brand_table.setItem(row, 1, category_item)
            self.brand_table.setItem(row, 2, sku_item)
            self.brand_table.setItem(row, 3, gmv_item)
            self.brand_table.setItem(row, 4, status_item)

        # Enable lại sorting
        self.brand_table.setSortingEnabled(True)

        # Cập nhật categories cho timeslot editor
        self.timeslot_editor.update_categories(list(self.brand_sorter.categories))

        # Cập nhật brands cho exclusive selection
        self.timeslot_editor.update_brands(self.brand_sorter.brands)
    

    
    def save_timeslots(self, data):
        """Lưu danh sách khung giờ từ TimeSlotEditor mới"""
        if "timeslots" in data:
            timeslots = data["timeslots"]

            # Clear existing timeslots
            self.brand_sorter.timeslots = []

            # Add new timeslots
            for ts in timeslots:
                self.brand_sorter.add_timeslot(
                    ts["name"],
                    ts["start_time"],
                    ts["end_time"],
                    ts["allowed_categories"],
                    ts["high_traffic"],
                    ts["min_sku"],
                    ts.get("exclusive", False),
                    ts.get("exclusive_brand"),
                    ts.get("exclusive_duration", 60)
                )

            print(f"📅 Đã lưu {len(timeslots)} khung giờ vào brand_sorter")
    
    def run_sorting_algorithm(self):
        # Kiểm tra dữ liệu
        if not self.brand_sorter.brands:
            QMessageBox.warning(self, "Thiếu dữ liệu", "Vui lòng nhập dữ liệu brands trước")
            return

        if not self.brand_sorter.timeslots:
            QMessageBox.warning(self, "Thiếu dữ liệu", "Vui lòng tạo khung giờ trước")
            return

        # API key được tích hợp trực tiếp vào code
        api_key = "********************************************************************************************************************************************************************"
        max_gmv_diff = self.gmv_diff_input.value() / 100.0

        # Hiển thị progress bar
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("Đang khởi tạo...")
        self.run_algorithm_btn.setEnabled(False)

        # Tạo và chạy worker thread
        self.sorting_worker = SortingWorker(self.brand_sorter, api_key, max_gmv_diff)
        self.sorting_worker.progress_updated.connect(self.update_progress)
        self.sorting_worker.sorting_completed.connect(self.on_sorting_completed)
        self.sorting_worker.sorting_failed.connect(self.on_sorting_failed)
        self.sorting_worker.start()

    @pyqtSlot(int, str)
    def update_progress(self, percentage, message):
        """Cập nhật progress bar và status"""
        self.progress_bar.setValue(percentage)
        self.status_label.setText(message)

    @pyqtSlot(dict)
    def on_sorting_completed(self, results):
        """Xử lý khi sắp xếp hoàn thành"""
        try:
            # Hiển thị kết quả
            self.display_results(results)

            # Cập nhật trạng thái của bảng brand
            self.update_brand_table(self.brand_sorter.brands)

            # Kiểm tra tính hợp lệ của kết quả
            errors = self.brand_sorter.validate_timeslot_assignment()
            if errors:
                error_msg = "\n".join(errors[:5])  # Chỉ hiển thị 5 lỗi đầu tiên
                if len(errors) > 5:
                    error_msg += f"\n... và {len(errors) - 5} lỗi khác"
                QMessageBox.warning(self, "Lưu ý", f"Kết quả sắp xếp có các vấn đề sau:\n{error_msg}")
            else:
                # Tính thống kê chi tiết
                total_brands = len(self.brand_sorter.brands)
                assigned_brands = len(self.brand_sorter.used_brands)
                unassigned_brands = total_brands - assigned_brands

                # Tính GMV stats
                total_gmv = sum(b["gmv"] for b in self.brand_sorter.brands)
                assigned_gmv = 0
                for brands in results.values():
                    assigned_gmv += sum(b["gmv"] for b in brands)

                success_msg = f"""🎉 SẮP XẾP THÀNH CÔNG!

📊 THỐNG KÊ:
• Tổng brands: {total_brands:,}
• Đã phân bổ: {assigned_brands:,} ({assigned_brands/total_brands*100:.1f}%)
• Chưa phân bổ: {unassigned_brands:,}

💰 GMV:
• Tổng GMV: {total_gmv:,.0f}
• GMV đã phân bổ: {assigned_gmv:,.0f} ({assigned_gmv/total_gmv*100:.1f}%)

🤖 Thuật toán đã tự động:
• Phân tích context mùa và thời gian
• Ưu tiên brands phù hợp từng khung giờ
• Cân bằng GMV giữa các timeslots
• Tối ưu theo hành vi người tiêu dùng"""

                QMessageBox.information(self, "🎯 Hoàn thành", success_msg)

        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"Lỗi hiển thị kết quả: {str(e)}")
        finally:
            # Ẩn progress bar và enable button
            self.progress_bar.setVisible(False)
            self.status_label.setText("Hoàn thành")
            self.run_algorithm_btn.setEnabled(True)

    @pyqtSlot(str)
    def on_sorting_failed(self, error_message):
        """Xử lý khi sắp xếp thất bại"""
        QMessageBox.critical(self, "Lỗi", f"Không thể sắp xếp brands: {error_message}")

        # Ẩn progress bar và enable button
        self.progress_bar.setVisible(False)
        self.status_label.setText("Lỗi")
        self.run_algorithm_btn.setEnabled(True)
    
    def display_results(self, results):
        """Hiển thị kết quả với tối ưu cho dataset lớn"""
        # Xóa các tab cũ
        while self.result_tabs.count() > 0:
            self.result_tabs.removeTab(0)

        # Tạo tab tổng quan trước
        overview_tab = self.create_overview_tab(results)
        self.result_tabs.addTab(overview_tab, "📊 Tổng quan")

        # Tạo tab cho mỗi khung giờ
        for timeslot_name, brands in results.items():
            if not brands:  # Bỏ qua khung giờ trống
                continue

            tab = QWidget()
            layout = QVBoxLayout()
            tab.setLayout(layout)

            # Thông tin tổng quan
            summary_layout = QHBoxLayout()
            avg_gmv = self.brand_sorter.calculate_avg_gmv(brands)
            total_sku = self.brand_sorter.calculate_total_sku_review(brands)

            summary_layout.addWidget(QLabel(f"📦 Brands: {len(brands)}"))
            summary_layout.addWidget(QLabel(f"💰 Avg GMV: {avg_gmv:,.0f}"))
            summary_layout.addWidget(QLabel(f"⭐ SKU Review: {total_sku:,}"))
            layout.addLayout(summary_layout)

            # Bảng brands với tối ưu
            table = QTableWidget()
            table.setColumnCount(4)
            table.setHorizontalHeaderLabels(["Brand", "Danh mục", "SKU Review", "GMV"])
            table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
            table.setSortingEnabled(True)  # Enable sorting

            # Disable updates để tăng tốc
            table.setUpdatesEnabled(False)
            table.setRowCount(len(brands))

            for row, brand in enumerate(brands):
                table.setItem(row, 0, QTableWidgetItem(brand["name"]))
                table.setItem(row, 1, QTableWidgetItem(brand["category"]))
                table.setItem(row, 2, QTableWidgetItem(str(brand["sku_review"])))
                table.setItem(row, 3, QTableWidgetItem(f"{brand['gmv']:,.0f}"))

            table.setUpdatesEnabled(True)
            layout.addWidget(table)

            # Thêm emoji cho tab name dựa trên thời gian
            tab_name = self.get_tab_emoji(timeslot_name) + " " + timeslot_name
            self.result_tabs.addTab(tab, tab_name)

    def create_overview_tab(self, results):
        """Tạo tab tổng quan với thống kê chi tiết"""
        tab = QWidget()
        layout = QVBoxLayout()
        tab.setLayout(layout)

        # Thống kê tổng quan
        total_assigned = sum(len(brands) for brands in results.values())
        total_brands = len(self.brand_sorter.brands)
        unassigned = total_brands - total_assigned

        stats_layout = QVBoxLayout()
        stats_layout.addWidget(QLabel(f"📊 THỐNG KÊ TỔNG QUAN"))
        stats_layout.addWidget(QLabel(f"• Tổng brands: {total_brands:,}"))
        stats_layout.addWidget(QLabel(f"• Đã phân bổ: {total_assigned:,} ({total_assigned/total_brands*100:.1f}%)"))
        stats_layout.addWidget(QLabel(f"• Chưa phân bổ: {unassigned:,}"))

        layout.addLayout(stats_layout)

        # Bảng thống kê theo khung giờ
        summary_table = QTableWidget()
        summary_table.setColumnCount(5)
        summary_table.setHorizontalHeaderLabels(["Khung giờ", "Số brands", "Avg GMV", "Total SKU", "% GMV"])
        summary_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

        non_empty_results = {k: v for k, v in results.items() if v}
        summary_table.setRowCount(len(non_empty_results))

        total_gmv_all = sum(self.brand_sorter.calculate_avg_gmv(brands) * len(brands)
                           for brands in non_empty_results.values())

        for row, (timeslot_name, brands) in enumerate(non_empty_results.items()):
            avg_gmv = self.brand_sorter.calculate_avg_gmv(brands)
            total_sku = self.brand_sorter.calculate_total_sku_review(brands)
            gmv_percentage = (avg_gmv * len(brands) / total_gmv_all * 100) if total_gmv_all > 0 else 0

            summary_table.setItem(row, 0, QTableWidgetItem(timeslot_name))
            summary_table.setItem(row, 1, QTableWidgetItem(str(len(brands))))
            summary_table.setItem(row, 2, QTableWidgetItem(f"{avg_gmv:,.0f}"))
            summary_table.setItem(row, 3, QTableWidgetItem(f"{total_sku:,}"))
            summary_table.setItem(row, 4, QTableWidgetItem(f"{gmv_percentage:.1f}%"))

        layout.addWidget(summary_table)
        return tab

    def get_tab_emoji(self, timeslot_name):
        """Lấy emoji phù hợp cho tab dựa trên tên khung giờ"""
        name_lower = timeslot_name.lower()
        if any(word in name_lower for word in ['sáng', 'morning', '6', '7', '8', '9']):
            return "🌅"
        elif any(word in name_lower for word in ['trưa', 'noon', '11', '12', '13']):
            return "☀️"
        elif any(word in name_lower for word in ['chiều', 'afternoon', '14', '15', '16', '17']):
            return "🌤️"
        elif any(word in name_lower for word in ['tối', 'evening', 'night', '18', '19', '20', '21']):
            return "🌙"
        else:
            return "⏰"
    
    def export_results(self):
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Lưu kết quả", "", "Excel Files (*.xlsx);;All Files (*)"
        )
        
        if file_path:
            try:
                self.brand_sorter.export_results(file_path)
                QMessageBox.information(self, "Thành công", f"Đã xuất kết quả ra file {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Lỗi", f"Không thể xuất kết quả: {str(e)}")

if __name__ == "__main__":
    # Enable high DPI scaling before creating QApplication
    QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)

    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("Brand Sorter")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("Shopee Vietnam")

    window = BrandManagerApp()
    window.show()

    # Tạo timer để check theme changes (optional - cho advanced users)
    from PyQt6.QtCore import QTimer
    theme_timer = QTimer()
    theme_timer.timeout.connect(window.refresh_theme)
    theme_timer.start(5000)  # Check every 5 seconds

    sys.exit(app.exec())